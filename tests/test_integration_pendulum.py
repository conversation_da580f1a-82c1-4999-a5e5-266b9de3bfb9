"""
大角度单摆MMSBVI系统集成测试 (Large-Angle Pendulum MMSBVI Integration Tests)

本测试文件验证所有核心组件的正确集成：
- CylindricalManifold + PhysicsInformedPendulumDrift + ManifoldMMDLoss
- UltraHeunIntegrator + FöllmerDriftNet + PrimalControlGradFlowSolver
- PendulumExperimentOrchestrator 完整流程

测试策略：
1. 组件初始化验证
2. 数据流正确性检查  
3. 数值精度验证
4. 端到端集成测试
5. 性能基准测试

Integration test for Large-Angle Pendulum MMSBVI system
验证大角度单摆MMSBVI系统的集成正确性
"""

import pytest
import jax
import jax.numpy as jnp
import jax.random as random
from jax import jit, vmap
import numpy as np
from typing import Dict, Any, Tuple
import logging
import time

# 设置JAX配置
jax.config.update('jax_enable_x64', True)  # 高精度计算

# 导入核心组件
import sys
import pathlib
root_dir = pathlib.Path(__file__).resolve().parents[1]
src_dir = root_dir / "src"
if str(src_dir) not in sys.path:
    sys.path.append(str(src_dir))

from mmsbvi.core.types import (
    NetworkConfig, ControlGradConfig, PerformanceConfig,
    Float, Array
)
from mmsbvi.manifolds.cylinder import CylindricalManifold
from mmsbvi.objectives.mmd import ManifoldMMDLoss
from mmsbvi.integrators.integrators import UltraHeunIntegrator
from mmsbvi.nets.flax_drift import FöllmerDriftNet
from mmsbvi.algorithms.control_grad import PrimalControlGradFlowSolver

# 导入实验组件
sys.path.append(str(root_dir / "src" / "experiments" / "large_angle_pendulum" / "mmsbvi"))
from nets import (
    PhysicsInformedPendulumDrift, 
    PhysicsInformedPendulumConfig,
    create_physics_informed_pendulum_drift
)
from experiment_orchestrator import (
    PendulumExperimentOrchestrator,
    PendulumExperimentConfig,
    create_default_pendulum_experiment
)

logger = logging.getLogger(__name__)


class TestComponentIntegration:
    """测试各组件之间的集成正确性"""
    
    @pytest.fixture
    def setup_components(self):
        """设置测试所需的所有组件"""
        # 物理配置
        physics_config = PhysicsInformedPendulumConfig(
            gravity=9.81,
            length=1.0, 
            mass=1.0,
            damping=0.1,
            physics_weight=1.0,
            network_weight=0.1
        )
        
        # 网络配置
        network_config = NetworkConfig(
            hidden_dims=[32, 32],  # 小网络用于快速测试
            n_layers=2,
            activation="silu",
            dropout_rate=0.0,
            time_encoding_dim=16
        )
        
        # 控制配置
        control_config = ControlGradConfig(
            state_dim=3,
            time_horizon=1.0,
            num_time_steps=20,  # 少步数用于快速测试
            batch_size=16,     # 小批量用于快速测试
            num_epochs=5,      # 少轮数用于快速测试
            learning_rate=1e-3,
            control_weight=1.0,
            boundary_weight=1.0
        )
        
        # 创建组件
        key = random.PRNGKey(42)
        manifold = CylindricalManifold(beta=1.0)
        
        physics_drift, params = create_physics_informed_pendulum_drift(
            physics_config=physics_config,
            network_config=network_config,
            random_key=key
        )
        
        mmd_loss = ManifoldMMDLoss(
            manifold=manifold,
            kappa=2.0,
            sigma_rbf=0.5,
            subsample_size=8
        )
        
        integrator = UltraHeunIntegrator()
        
        return {
            "physics_config": physics_config,
            "network_config": network_config,
            "control_config": control_config,
            "manifold": manifold,
            "physics_drift": physics_drift,
            "mmd_loss": mmd_loss,
            "integrator": integrator,
            "params": params,
            "key": key
        }
    
    def test_manifold_physics_integration(self, setup_components):
        """测试流形与物理漂移的集成"""
        components = setup_components
        manifold = components["manifold"]
        physics_drift = components["physics_drift"]
        params = components["params"]
        
        # 测试状态转换
        theta_omega = jnp.array([[0.5, 1.0], [1.0, -0.5], [2.0, 0.0]])  # 批量状态
        embedded_states = vmap(manifold.from_angle_velocity)(theta_omega)
        
        # 验证嵌入约束
        cos_theta, sin_theta = embedded_states[:, 0], embedded_states[:, 1]
        constraint_violation = jnp.abs(cos_theta**2 + sin_theta**2 - 1.0)
        assert jnp.all(constraint_violation < 1e-10), f"嵌入约束违规: {jnp.max(constraint_violation)}"
        
        # 测试物理漂移计算
        t_test = jnp.array([0.0, 0.5, 1.0])
        u_test = jnp.zeros((3, 1))  # 零控制
        
        # 计算漂移
        drift_values = vmap(
            lambda x, t, u: physics_drift.compute_total_drift(x, t, u, params)
        )(embedded_states, t_test, u_test)
        
        # 验证漂移维度
        assert drift_values.shape == (3, 3), f"漂移形状错误: {drift_values.shape}"
        
        # 验证切空间约束 (cos θ * d(cos θ) + sin θ * d(sin θ) = 0)
        for i in range(3):
            constraint = (cos_theta[i] * drift_values[i, 0] + 
                         sin_theta[i] * drift_values[i, 1])
            assert abs(constraint) < 1e-8, f"切空间约束违规: {constraint}"
        
        print("✅ 流形-物理集成测试通过")
    
    def test_mmd_manifold_integration(self, setup_components):
        """测试MMD损失与流形的集成"""
        components = setup_components
        manifold = components["manifold"]
        mmd_loss = components["mmd_loss"]
        key = components["key"]
        
        # 生成测试样本
        key1, key2, key3 = random.split(key, 3)
        
        # 样本1: 集中在θ=0附近
        angles1 = random.normal(key1, (10,)) * 0.1
        velocities1 = random.normal(key1, (10,)) * 0.1
        samples1 = vmap(manifold.from_angle_velocity)(
            jnp.stack([angles1, velocities1], axis=-1)
        )
        
        # 样本2: 集中在θ=π附近
        angles2 = random.normal(key2, (10,)) * 0.1 + jnp.pi
        velocities2 = random.normal(key2, (10,)) * 0.1
        samples2 = vmap(manifold.from_angle_velocity)(
            jnp.stack([angles2, velocities2], axis=-1)
        )
        
        # 计算MMD距离
        mmd_dist, diagnostics = mmd_loss(samples1, samples2, key3, return_diagnostics=True)
        
        # 验证MMD计算
        assert isinstance(mmd_dist, (float, jnp.ndarray)), "MMD距离类型错误"
        assert not jnp.isnan(mmd_dist), "MMD距离为NaN"
        assert "von_mises_term" in diagnostics, "缺少Von Mises项诊断"
        assert "rbf_term" in diagnostics, "缺少RBF项诊断"
        
        # 不同分布的MMD应该 > 0
        assert mmd_dist > 0, f"不同分布的MMD应为正: {mmd_dist}"
        
        # 相同分布的MMD应该 ≈ 0
        mmd_same, _ = mmd_loss(samples1, samples1, key3, return_diagnostics=True)
        assert abs(mmd_same) < 0.1, f"相同分布的MMD应接近0: {mmd_same}"
        
        print("✅ MMD-流形集成测试通过")
    
    def test_integrator_manifold_projection(self, setup_components):
        """测试积分器与流形投影的集成"""
        components = setup_components
        manifold = components["manifold"]
        integrator = components["integrator"]
        physics_drift = components["physics_drift"]
        params = components["params"]
        key = components["key"]
        
        # 创建带投影的积分步骤
        def projected_step_fn(t: float, state: Float[Array, "3"], 
                            drift_fn: callable, diffusion_fn: callable,
                            dt: float, key: jax.random.PRNGKey) -> Float[Array, "3"]:
            """积分步骤 + 流形投影"""
            # 执行标准Heun步骤
            next_state = integrator.step(t, state, drift_fn, diffusion_fn, dt, key)
            # 投影回流形
            projected_state = manifold.project(next_state)
            return projected_state
        
        # 定义漂移和扩散函数
        def drift_fn(t, x):
            u = jnp.zeros((1,))  # 零控制
            return physics_drift.compute_total_drift(x, t, u, params)
        
        def diffusion_fn(t, x):
            return jnp.array([0.0, 0.0, 0.1])  # 仅在角速度上添加噪声
        
        # 测试积分步骤
        initial_state = jnp.array([1.0, 0.0, 0.5])  # θ=0, ω=0.5
        dt = 0.01
        t = 0.0
        
        # 执行多步积分
        state = initial_state
        for i in range(10):
            step_key = random.split(key, i+2)[i+1]
            state = projected_step_fn(t + i*dt, state, drift_fn, diffusion_fn, dt, step_key)
            
            # 验证流形约束
            cos_theta, sin_theta = state[0], state[1]
            constraint_violation = abs(cos_theta**2 + sin_theta**2 - 1.0)
            assert constraint_violation < 1e-10, f"步骤{i}流形约束违规: {constraint_violation}"
        
        print("✅ 积分器-流形投影集成测试通过")
    
    def test_end_to_end_experiment_setup(self, setup_components):
        """测试端到端实验设置"""
        # 创建默认实验配置
        experiment = create_default_pendulum_experiment()
        
        # 设置较小的配置用于快速测试
        experiment.config.warmup_epochs = 2
        experiment.config.main_epochs = 3
        experiment.config.control_config.batch_size = 8
        experiment.config.control_config.num_time_steps = 10
        
        # 测试组件初始化
        try:
            experiment.setup()
            assert experiment.manifold is not None, "流形未初始化"
            assert experiment.physics_drift is not None, "物理漂移未初始化"
            assert experiment.mmd_loss is not None, "MMD损失未初始化"  
            assert experiment.solver is not None, "求解器未初始化"
            assert experiment.integrator is not None, "积分器未初始化"
            print("✅ 实验组件初始化测试通过")
        except Exception as e:
            pytest.fail(f"组件初始化失败: {str(e)}")
        
        # 测试验证函数
        try:
            dummy_state = {"params": experiment.initial_params}
            validation_key = random.PRNGKey(123)
            validation_results = experiment.run_pendulum_validation(dummy_state, validation_key)
            
            # 验证返回的指标
            required_metrics = [
                "mean_winding_number", "std_winding_number", 
                "circular_variance", "mean_energy", "energy_variance",
                "mean_geodesic_step", "max_geodesic_step",
                "max_constraint_violation", "mean_constraint_violation"
            ]
            
            for metric in required_metrics:
                assert metric in validation_results, f"缺少验证指标: {metric}"
                assert not jnp.isnan(validation_results[metric]), f"指标{metric}为NaN"
            
            print("✅ 验证指标测试通过")
        except Exception as e:
            pytest.fail(f"验证指标计算失败: {str(e)}")
    
    def test_training_compatibility(self, setup_components):
        """测试训练兼容性（不运行完整训练）"""
        # 创建小规模实验
        experiment = create_default_pendulum_experiment()
        experiment.config.warmup_epochs = 1
        experiment.config.main_epochs = 1 
        experiment.config.control_config.batch_size = 4
        experiment.config.control_config.num_time_steps = 5
        
        # 初始化组件
        experiment.setup()
        
        # 测试单个训练阶段的设置（不实际运行）
        try:
            # 生成目标样本
            target_key = random.PRNGKey(456)
            num_target_samples = 4
            target_angles = random.uniform(target_key, (num_target_samples,), minval=0.0, maxval=2*jnp.pi)
            target_velocities = jnp.zeros((num_target_samples,))
            target_samples = experiment.manifold.from_angle_velocity(
                jnp.stack([target_angles, target_velocities], axis=-1)
            )
            
            # 验证目标样本形状和约束
            assert target_samples.shape == (num_target_samples, 3), "目标样本形状错误"
            cos_theta, sin_theta = target_samples[:, 0], target_samples[:, 1]
            constraint_violations = jnp.abs(cos_theta**2 + sin_theta**2 - 1.0)
            assert jnp.all(constraint_violations < 1e-10), "目标样本约束违规"
            
            print("✅ 训练兼容性测试通过")
        except Exception as e:
            pytest.fail(f"训练兼容性测试失败: {str(e)}")


class TestPerformanceBenchmarks:
    """性能基准测试"""
    
    def test_component_performance(self, setup_components):
        """测试各组件的性能"""
        components = setup_components
        manifold = components["manifold"]
        physics_drift = components["physics_drift"]
        mmd_loss = components["mmd_loss"]
        params = components["params"]
        key = components["key"]
        
        # 批量测试数据
        batch_size = 100
        test_states = random.normal(key, (batch_size, 3))
        test_states = vmap(manifold.project)(test_states)  # 投影到流形
        test_times = random.uniform(key, (batch_size,))
        test_controls = jnp.zeros((batch_size, 1))
        
        # 性能测试: 物理漂移计算
        start_time = time.time()
        drift_results = vmap(
            lambda x, t, u: physics_drift.compute_total_drift(x, t, u, params)
        )(test_states, test_times, test_controls)
        physics_time = time.time() - start_time
        
        # 性能测试: MMD损失计算
        samples1 = test_states[:50]
        samples2 = test_states[50:]
        start_time = time.time()
        mmd_result, _ = mmd_loss(samples1, samples2, key, return_diagnostics=True)
        mmd_time = time.time() - start_time
        
        # 性能报告
        print(f"📊 性能基准测试结果:")
        print(f"   物理漂移计算 ({batch_size}样本): {physics_time:.4f}s")
        print(f"   MMD损失计算 (50x50样本): {mmd_time:.4f}s") 
        print(f"   漂移计算吞吐量: {batch_size/physics_time:.1f} 样本/秒")
        
        # 验证结果有效性
        assert drift_results.shape == (batch_size, 3), "漂移结果形状错误"
        assert not jnp.any(jnp.isnan(drift_results)), "漂移结果包含NaN"
        assert not jnp.isnan(mmd_result), "MMD结果为NaN"
        
        print("✅ 性能基准测试通过")


def test_full_integration():
    """完整集成测试入口"""
    print("🚀 开始大角度单摆MMSBVI系统集成测试")
    
    # 运行所有测试
    pytest.main([__file__ + "::TestComponentIntegration", "-v"])
    pytest.main([__file__ + "::TestPerformanceBenchmarks", "-v"])  
    
    print("🎉 所有集成测试完成!")


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    test_full_integration()