"""
Manifold MMD Loss Tests  
流形MMD损失测试

这个测试套件为ManifoldMMDLoss组件提供严格的单元测试，验证MMD的数学性质、
核函数正确性、无偏估计器和数值稳定性。这是确保大单摆实验分布匹配
数学正确性的关键防线。

⚠️  CRITICAL UNDERSTANDING - MMD无偏估计器的负值性质:
    
    MMD²无偏估计器(U-统计量)在有限样本下可能为负值，这是正常的统计性质！
    
    - 理论MMD²(P,Q) ≥ 0 总是成立
    - 但无偏估计器 MMD²u(P,Q) 可能 < 0，特别是当：
      * 样本量小时
      * 两个分布相同或非常相似时  
      * 估计器方差大于真实MMD²值时
    
    这不是实现错误，而是U-统计量理论的正常表现。参考文献：
    - Gretton et al. (2012) "A Kernel Two-Sample Test"
    - Sutherland et al. (2017) MMD variance estimation
    
    测试因此检查：
    - 有限性和合理性，而不是非负性
    - 对称性和无偏性的统计性质
    - 分布区分能力的相对比较

Key Test Categories:
1. 数学性质验证 - MMD基本性质(自相关性、对称性、分布区分)
2. 核函数测试 - Von Mises核、RBF核和张量积核的数学性质
3. 无偏估计器验证 - 统计学正确性和收敛性
4. 数值稳定性测试 - 边界条件和极值处理
5. 性能验证 - JIT编译、批量处理和子采样
6. 集成测试 - 与CylindricalManifold的集成

Mathematical Foundation:
- MMD²(P,Q) = ||μ_P - μ_Q||²_H
- 无偏估计器: E[k(X,X')] + E[k(Y,Y')] - 2E[k(X,Y)] (可能为负!)
- Von Mises核: k_VM(θ₁,θ₂) = exp(κ cos(θ₁-θ₂))
- RBF核: k_RBF(ω₁,ω₂) = exp(-||ω₁-ω₂||²/(2σ²))
- 张量积核: k(x₁,x₂) = k_VM(θ₁,θ₂) × k_RBF(ω₁,ω₂)
"""

import pytest
import jax
import jax.numpy as jnp
import jax.random
import numpy as np
from typing import Tuple, Dict
import sys
import pathlib

# Add src to path for imports
root_dir = pathlib.Path(__file__).resolve().parents[1]
src_dir = root_dir / "src"
if str(src_dir) not in sys.path:
    sys.path.append(str(src_dir))

from mmsbvi.objectives.mmd import ManifoldMMDLoss
from mmsbvi.manifolds.cylinder import CylindricalManifold

# Enable 64-bit precision for theoretical validation
jax.config.update('jax_enable_x64', True)


class TestManifoldMMDLoss:
    """ManifoldMMDLoss核心测试套件"""
    
    def setup_method(self):
        """每个测试前的设置"""
        self.manifold = CylindricalManifold(beta=1.0)
        self.mmd_loss = ManifoldMMDLoss(
            manifold=self.manifold,
            kappa=1.0,
            sigma_rbf=1.0,
            subsample_size=None
        )
        self.key = jax.random.PRNGKey(42)
        
        # 通用测试数据
        self.test_key1, self.test_key2 = jax.random.split(self.key, 2)
    
    # ===== 数学性质测试 =====
    
    def test_mmd_mathematical_properties(self):
        """测试MMD的基本数学性质
        
        注意：MMD²无偏估计器在有限样本下可能为负值，这是U-统计量的正常性质，
        不违反MMD的数学理论。真实的MMD²≥0，但无偏估计器可能为负。
        """
        # 生成测试数据
        key1, key2, key3 = jax.random.split(self.key, 3)
        
        # 相同分布的样本
        n = 50
        p_samples = jax.random.normal(key1, (n, 3))
        # 投影到流形上
        p_samples = self.manifold.project(p_samples)
        
        # 不同分布的样本 - 在ω维度有偏移
        q_samples = jax.random.normal(key2, (n, 3)) 
        q_samples = q_samples.at[:, 2].add(2.0)  # ω维度偏移
        q_samples = self.manifold.project(q_samples)
        
        # 1. 自相关性：E[MMD²u(P,P)] = 0，但单次估计可能为负
        mmd_pp, _ = self.mmd_loss.compute_mmd_squared(p_samples, p_samples, key3)
        assert jnp.isfinite(mmd_pp), f"MMD自相关应为有限值，得到: {mmd_pp}"
        assert abs(mmd_pp) < 1.0, f"MMD自相关的绝对值应合理，得到: {mmd_pp}"
        
        # 2. 区分性：不同分布的MMD应该显著非零（可能为负但绝对值大）
        mmd_pq, _ = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, key3)
        assert jnp.isfinite(mmd_pq), f"MMD应为有限值，得到: {mmd_pq}"
        
        # 3. 对称性：MMD(P,Q) = MMD(Q,P)
        mmd_qp, _ = self.mmd_loss.compute_mmd_squared(q_samples, p_samples, key3)
        symmetry_error = abs(mmd_pq - mmd_qp)
        assert symmetry_error < 1e-12, f"MMD对称性误差过大: {symmetry_error}"
        
        # 4. 分布区分能力：不同分布的MMD绝对值应明显大于相同分布
        assert abs(mmd_pq) > abs(mmd_pp), f"不同分布MMD绝对值应大于相同分布: |{mmd_pq:.4f}| vs |{mmd_pp:.4f}|"
        
        print(f"✓ MMD数学性质测试通过 - 自相关: {mmd_pp:.2e}, 区分: {mmd_pq:.4f}, 对称误差: {symmetry_error:.2e}")
    
    def test_kernel_properties(self):
        """测试各个核函数的数学性质"""
        # 生成测试点
        key1, key2 = jax.random.split(self.key, 2)
        x1 = self.manifold.project(jax.random.normal(key1, (3,)))
        x2 = self.manifold.project(jax.random.normal(key2, (3,))) 
        
        # Von Mises核测试
        vm_k_11 = self.mmd_loss._von_mises_kernel(x1, x1)
        vm_k_12 = self.mmd_loss._von_mises_kernel(x1, x2)
        vm_k_21 = self.mmd_loss._von_mises_kernel(x2, x1)
        
        # 对称性
        assert abs(vm_k_12 - vm_k_21) < 1e-13, f"Von Mises核对称性误差: {abs(vm_k_12 - vm_k_21)}"
        
        # 自相关性：k(x,x) = exp(κ)，考虑数值精度
        expected_self_kernel = jnp.exp(self.mmd_loss.kappa)
        assert abs(vm_k_11 - expected_self_kernel) < 1e-7, \
            f"Von Mises核自相关错误: 期望{expected_self_kernel}, 得到{vm_k_11}"
        
        # 正值性
        assert vm_k_12 > 0, f"Von Mises核应为正值，得到: {vm_k_12}"
        
        # RBF核测试
        rbf_k_11 = self.mmd_loss._rbf_kernel(x1, x1)
        rbf_k_12 = self.mmd_loss._rbf_kernel(x1, x2)
        rbf_k_21 = self.mmd_loss._rbf_kernel(x2, x1)
        
        # 对称性
        assert abs(rbf_k_12 - rbf_k_21) < 1e-14, f"RBF核对称性误差: {abs(rbf_k_12 - rbf_k_21)}"
        
        # 自相关性：k(x,x) = 1
        assert abs(rbf_k_11 - 1.0) < 1e-12, f"RBF核自相关应为1，得到: {rbf_k_11}"
        
        # 正值性
        assert rbf_k_12 > 0, f"RBF核应为正值，得到: {rbf_k_12}"
        
        # 张量积核测试
        tp_k_11 = self.mmd_loss._tensor_product_kernel(x1, x1)
        tp_k_12 = self.mmd_loss._tensor_product_kernel(x1, x2)
        tp_k_21 = self.mmd_loss._tensor_product_kernel(x2, x1)
        
        # 对称性
        assert abs(tp_k_12 - tp_k_21) < 1e-14, f"张量积核对称性误差: {abs(tp_k_12 - tp_k_21)}"
        
        # 张量积性质：k(x,y) = k_VM(x,y) * k_RBF(x,y)
        expected_tp = vm_k_12 * rbf_k_12
        assert abs(tp_k_12 - expected_tp) < 1e-12, \
            f"张量积核计算错误: 期望{expected_tp}, 得到{tp_k_12}"
        
        # 自相关性：k(x,x) = exp(κ) * 1 = exp(κ)，考虑数值精度
        assert abs(tp_k_11 - expected_self_kernel) < 1e-7, \
            f"张量积核自相关错误: 期望{expected_self_kernel}, 得到{tp_k_11}"
        
        print(f"✓ 核函数性质测试通过 - VM自相关: {vm_k_11:.6f}, RBF自相关: {rbf_k_11:.6f}")
    
    def test_periodic_property_von_mises(self):
        """测试Von Mises核的周期性"""
        # 创建角度相同但表示不同的点对(利用周期性)
        theta = 0.5
        omega = 1.0
        
        # 原始点
        x1 = self.manifold.from_angle_velocity(theta, omega)
        
        # 相同角度 + 2π的点
        x2 = self.manifold.from_angle_velocity(theta + 2*jnp.pi, omega)
        
        # Von Mises核值应该相同(周期性)
        k1 = self.mmd_loss._von_mises_kernel(x1, x2)
        expected = jnp.exp(self.mmd_loss.kappa)  # cos(0) = 1
        
        assert abs(k1 - expected) < 1e-12, \
            f"Von Mises核周期性失败: 期望{expected}, 得到{k1}"
        
        print(f"✓ Von Mises核周期性测试通过: {k1:.6f}")
    
    def test_unbiased_estimator_validation(self):
        """测试无偏MMD²估计器的统计性质"""
        # 使用已知分布进行蒙特卡洛验证
        n_runs = 20
        n_samples = 30
        mmd_estimates = []
        
        for i in range(n_runs):
            key_run = jax.random.PRNGKey(i)
            key1, key2, key3 = jax.random.split(key_run, 3)
            
            # 生成两个不同均值的高斯分布
            p_samples = jax.random.normal(key1, (n_samples, 3))
            q_samples = jax.random.normal(key2, (n_samples, 3)) + jnp.array([0, 0, 1.0])
            
            # 投影到流形
            p_samples = self.manifold.project(p_samples)
            q_samples = self.manifold.project(q_samples)
            
            mmd_val, diagnostics = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, key3)
            mmd_estimates.append(float(mmd_val))
            
            # 验证诊断信息的一致性
            reconstructed = diagnostics["pp_term"] + diagnostics["qq_term"] + diagnostics["pq_term"]
            assert abs(mmd_val - reconstructed) < 1e-12, \
                f"诊断信息重构误差: {abs(mmd_val - reconstructed)}"
        
        mmd_estimates = np.array(mmd_estimates)
        mean_mmd = np.mean(mmd_estimates)
        std_mmd = np.std(mmd_estimates)
        
        # 验证估计值的合理性
        assert mean_mmd > 0, f"估计的MMD均值应为正，得到: {mean_mmd}"
        assert std_mmd < mean_mmd / 2, f"估计方差过大，均值: {mean_mmd}, 标准差: {std_mmd}"
        
        print(f"✓ 无偏估计器验证通过 - 均值: {mean_mmd:.4f}, 标准差: {std_mmd:.4f}")
        
    # ===== 数值稳定性测试 =====
    
    def test_parameter_boundaries(self):
        """测试参数的边界条件"""
        # 小样本测试
        key1, key2, key3 = jax.random.split(self.key, 3)
        small_p = self.manifold.project(jax.random.normal(key1, (2, 3)))
        small_q = self.manifold.project(jax.random.normal(key2, (2, 3)))
        
        mmd_small, _ = self.mmd_loss.compute_mmd_squared(small_p, small_q, key3)
        assert jnp.isfinite(mmd_small), f"小样本MMD应为有限值，得到: {mmd_small}"
        assert mmd_small >= 0, f"小样本MMD应非负，得到: {mmd_small}"
        
        # 极值参数测试 - 大κ
        mmd_large_kappa = ManifoldMMDLoss(
            manifold=self.manifold,
            kappa=100.0,  # 大κ值
            sigma_rbf=1.0
        )
        mmd_lk, _ = mmd_large_kappa.compute_mmd_squared(small_p, small_q, key3)
        assert jnp.isfinite(mmd_lk), f"大κ的MMD应为有限值，得到: {mmd_lk}"
        
        # 极值参数测试 - 小σ
        mmd_small_sigma = ManifoldMMDLoss(
            manifold=self.manifold,
            kappa=1.0,
            sigma_rbf=0.01  # 小σ值
        )
        mmd_ss, _ = mmd_small_sigma.compute_mmd_squared(small_p, small_q, key3)
        assert jnp.isfinite(mmd_ss), f"小σ的MMD应为有限值，得到: {mmd_ss}"
        
        print(f"✓ 参数边界测试通过 - 小样本: {mmd_small:.6f}, 大κ: {mmd_lk:.6f}, 小σ: {mmd_ss:.6f}")
    
    def test_numerical_stability_extreme_values(self):
        """测试极值输入的数值稳定性"""
        # 极大坐标值
        extreme_coords = jnp.array([
            [1e3, 1e3, 1e6],
            [-1e3, -1e3, -1e6]
        ])
        extreme_projected = self.manifold.project(extreme_coords)
        
        mmd_extreme, _ = self.mmd_loss.compute_mmd_squared(
            extreme_projected, extreme_projected, self.key
        )
        
        assert jnp.isfinite(mmd_extreme), f"极值坐标MMD应为有finite值，得到: {mmd_extreme}"
        # 注意：无偏估计器可能为负，我们只检查绝对值的合理性
        assert abs(mmd_extreme) < 10.0, f"极值自相关MMD绝对值应合理，得到: {mmd_extreme}"
        
        # 测试极近样本的数值精度
        base_sample = self.manifold.project(jnp.array([[1.0, 0.0, 0.0]]))
        near_sample = base_sample + jnp.array([[1e-12, 1e-12, 1e-12]])
        near_projected = self.manifold.project(near_sample)
        
        mmd_near, _ = self.mmd_loss.compute_mmd_squared(
            base_sample, near_projected, self.key
        )
        
        assert jnp.isfinite(mmd_near), f"极近样本MMD应为有限值，得到: {mmd_near}"
        # 无偏估计器可能为负，检查绝对值的合理性
        assert abs(mmd_near) < 1.0, f"极近样本MMD绝对值应合理，得到: {mmd_near}"
        
        print(f"✓ 数值稳定性测试通过 - 极值: {mmd_extreme:.2e}, 极近: {mmd_near:.2e}")
    
    def test_insufficient_samples_handling(self):
        """测试样本不足的处理"""
        # 单样本测试
        single_sample = self.manifold.project(jax.random.normal(self.key, (1, 3)))
        
        mmd_single, diag_single = self.mmd_loss.compute_mmd_squared(
            single_sample, single_sample, self.key
        )
        
        # 应该返回0并设置警告标志
        assert mmd_single == 0.0, f"单样本MMD应为0，得到: {mmd_single}"
        assert diag_single["warning"] == 1.0, "单样本应设置警告标志"
        
        print(f"✓ 样本不足处理测试通过")
    
    # ===== 性能和一致性测试 =====
    
    def test_jit_consistency(self):
        """测试JIT编译的一致性"""
        key1, key2, key3 = jax.random.split(self.key, 3)
        p_samples = self.manifold.project(jax.random.normal(key1, (20, 3)))
        q_samples = self.manifold.project(jax.random.normal(key2, (20, 3)))
        
        # 非JIT结果（首次调用会触发编译）
        mmd1, diag1 = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, key3)
        
        # JIT编译后的结果
        mmd2, diag2 = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, key3)
        
        # 结果应完全一致
        assert abs(mmd1 - mmd2) < 1e-15, f"JIT前后MMD不一致: {abs(mmd1 - mmd2)}"
        
        # 诊断信息一致性
        for key in diag1.keys():
            if key != "warning":  # 跳过warning标志
                diff = abs(diag1[key] - diag2[key])
                assert diff < 1e-15, f"JIT前后诊断信息{key}不一致: {diff}"
        
        print(f"✓ JIT一致性测试通过 - MMD: {mmd1:.6f}")
    
    def test_batch_consistency(self):
        """测试批量计算的一致性"""
        # 生成测试数据
        key1, key2 = jax.random.split(self.key, 2)
        p_samples = self.manifold.project(jax.random.normal(key1, (10, 3)))
        q_samples = self.manifold.project(jax.random.normal(key2, (10, 3)))
        
        # 不同批量大小的一致性（这里指相同数据的重复计算）
        keys = jax.random.split(jax.random.PRNGKey(123), 5)
        results = []
        
        for key in keys:
            mmd, _ = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, key)
            results.append(float(mmd))
        
        # 由于使用相同数据，结果应该一致（随机性仅来自key，但这里计算是确定的）
        max_diff = max(results) - min(results)
        assert max_diff < 1e-14, f"批量计算不一致，最大差异: {max_diff}"
        
        print(f"✓ 批量一致性测试通过 - 结果范围: [{min(results):.8f}, {max(results):.8f}]")
    
    def test_subsampling_accuracy(self):
        """测试子采样模式的准确性"""
        # 生成大数据集
        key1, key2 = jax.random.split(self.key, 2)
        large_p = self.manifold.project(jax.random.normal(key1, (200, 3)))
        large_q = self.manifold.project(jax.random.normal(key2, (200, 3)))
        large_q = large_q.at[:, 2].add(1.0)  # 添加偏移以确保MMD>0
        
        # 完整计算
        mmd_full, _ = self.mmd_loss.compute_mmd_squared(large_p, large_q, self.key)
        
        # 子采样计算
        mmd_subsampled = ManifoldMMDLoss(
            manifold=self.manifold,
            kappa=1.0,
            sigma_rbf=1.0,
            subsample_size=50
        )
        
        # 多次子采样以评估方差
        subsample_results = []
        for i in range(10):
            key_sub = jax.random.PRNGKey(i + 100)
            mmd_sub = mmd_subsampled.compute_mmd_squared_subsampled(
                large_p, large_q, key_sub
            )
            subsample_results.append(float(mmd_sub))
        
        mean_sub = np.mean(subsample_results)
        std_sub = np.std(subsample_results)
        
        # 子采样结果应在合理范围内
        relative_error = abs(mean_sub - mmd_full) / mmd_full
        assert relative_error < 0.3, f"子采样相对误差过大: {relative_error:.4f}"
        
        # 方差应该合理
        cv = std_sub / mean_sub  # 变异系数
        assert cv < 0.5, f"子采样变异系数过大: {cv:.4f}"
        
        print(f"✓ 子采样准确性测试通过 - 完整: {mmd_full:.4f}, 子采样均值: {mean_sub:.4f} ± {std_sub:.4f}")
    
    # ===== 集成测试 =====
    
    def test_manifold_integration(self):
        """测试与CylindricalManifold的集成"""
        # 不同beta参数的流形
        manifold_beta2 = CylindricalManifold(beta=2.0)
        mmd_beta2 = ManifoldMMDLoss(
            manifold=manifold_beta2,
            kappa=1.0,
            sigma_rbf=1.0
        )
        
        # 生成测试数据 - 角度相同，速度不同
        key1, key2 = jax.random.split(self.key, 2)
        
        # 相同角度分布，不同速度分布
        angles = jax.random.uniform(key1, (30,)) * 2 * jnp.pi
        omegas_p = jax.random.normal(key1, (30,)) * 0.5
        omegas_q = jax.random.normal(key2, (30,)) * 2.0  # 更大的速度方差
        
        p_samples = jax.vmap(lambda t, w: self.manifold.from_angle_velocity(t, w))(angles, omegas_p)
        q_samples = jax.vmap(lambda t, w: self.manifold.from_angle_velocity(t, w))(angles, omegas_q)
        
        # beta=1时的MMD
        mmd_beta1, _ = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, self.key)
        
        # beta=2时的MMD（速度差异权重更大）
        mmd_beta2_val, _ = mmd_beta2.compute_mmd_squared(p_samples, q_samples, self.key)
        
        # beta更大时，速度差异的权重应该更大，MMD绝对值应该更大
        # 注意：由于无偏估计器可能为负，我们比较绝对值
        assert abs(mmd_beta2_val) >= abs(mmd_beta1) * 0.9, \
            f"beta=2时的MMD绝对值应不小于beta=1: |β1|={abs(mmd_beta1):.4f}, |β2|={abs(mmd_beta2_val):.4f}"
        
        print(f"✓ 流形集成测试通过 - β=1: {mmd_beta1:.4f}, β=2: {mmd_beta2_val:.4f}")
    
    def test_coordinate_invariance(self):
        """测试坐标变换不变性"""
        # 生成角度-速度坐标
        key1, key2 = jax.random.split(self.key, 2)
        
        n = 20
        thetas_p = jax.random.uniform(key1, (n,)) * 2 * jnp.pi
        omegas_p = jax.random.normal(key1, (n,))
        
        thetas_q = jax.random.uniform(key2, (n,)) * 2 * jnp.pi  
        omegas_q = jax.random.normal(key2, (n,)) + 1.0
        
        # 方法1：直接从角度-速度生成嵌入坐标
        p_samples_direct = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(thetas_p, omegas_p)
        
        q_samples_direct = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(thetas_q, omegas_q)
        
        # 方法2：先生成任意坐标再投影
        p_raw = jnp.column_stack([jnp.cos(thetas_p), jnp.sin(thetas_p), omegas_p])
        q_raw = jnp.column_stack([jnp.cos(thetas_q), jnp.sin(thetas_q), omegas_q])
        
        p_samples_projected = self.manifold.project(p_raw)
        q_samples_projected = self.manifold.project(q_raw)
        
        # 计算MMD
        mmd_direct, _ = self.mmd_loss.compute_mmd_squared(
            p_samples_direct, q_samples_direct, self.key
        )
        mmd_projected, _ = self.mmd_loss.compute_mmd_squared(
            p_samples_projected, q_samples_projected, self.key
        )
        
        # 结果应该一致（在数值精度内）
        assert abs(mmd_direct - mmd_projected) < 1e-8, \
            f"坐标变换不变性失败: 直接={mmd_direct:.8f}, 投影={mmd_projected:.8f}"
        
        print(f"✓ 坐标不变性测试通过 - MMD: {mmd_direct:.6f}")


class TestMMDStatisticalProperties:
    """MMD统计性质专项测试"""
    
    def setup_method(self):
        """测试设置"""
        self.manifold = CylindricalManifold(beta=1.0)
        self.mmd_loss = ManifoldMMDLoss(
            manifold=self.manifold,
            kappa=2.0,
            sigma_rbf=1.0
        )
        self.key = jax.random.PRNGKey(42)
    
    def test_distribution_discrimination_power(self):
        """测试分布区分能力"""
        n = 50
        key1, key2, key3, key4 = jax.random.split(self.key, 4)
        
        # 基准分布
        base_angles = jax.random.uniform(key1, (n,)) * 2 * jnp.pi
        base_omegas = jax.random.normal(key1, (n,))
        base_samples = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(base_angles, base_omegas)
        
        # 不同类型的分布偏移
        test_cases = []
        
        # 1. 角度分布偏移
        concentrated_angles = jax.random.normal(key2, (n,)) * 0.1  # 集中在0附近
        angle_shifted_samples = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(concentrated_angles, base_omegas)
        
        mmd_angle, _ = self.mmd_loss.compute_mmd_squared(
            base_samples, angle_shifted_samples, key3
        )
        test_cases.append(("角度集中", mmd_angle))
        
        # 2. 速度分布偏移
        shifted_omegas = jax.random.normal(key3, (n,)) + 2.0  # 均值偏移
        velocity_shifted_samples = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(base_angles, shifted_omegas)
        
        mmd_velocity, _ = self.mmd_loss.compute_mmd_squared(
            base_samples, velocity_shifted_samples, key4
        )
        test_cases.append(("速度偏移", mmd_velocity))
        
        # 3. 双重偏移
        double_shifted_samples = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(concentrated_angles, shifted_omegas)
        
        mmd_double, _ = self.mmd_loss.compute_mmd_squared(
            base_samples, double_shifted_samples, key4
        )
        test_cases.append(("双重偏移", mmd_double))
        
        # 验证区分能力的单调性
        assert mmd_double > max(mmd_angle, mmd_velocity), \
            f"双重偏移MMD应最大: 角度={mmd_angle:.4f}, 速度={mmd_velocity:.4f}, 双重={mmd_double:.4f}"
        
        # 所有MMD都应为正
        for name, mmd_val in test_cases:
            assert mmd_val > 0, f"{name}的MMD应为正，得到: {mmd_val}"
        
        print("✓ 分布区分能力测试通过:")
        for name, mmd_val in test_cases:
            print(f"  {name}: {mmd_val:.4f}")
    
    def test_sample_size_convergence(self):
        """测试样本量对估计精度的影响"""
        # 固定的分布参数
        key1, key2 = jax.random.split(self.key, 2)
        
        sample_sizes = [10, 20, 50, 100, 200]
        mmd_estimates = []
        
        for n in sample_sizes:
            # 生成固定偏移的分布
            p_samples = self.manifold.project(jax.random.normal(key1, (n, 3)))
            q_samples = self.manifold.project(jax.random.normal(key2, (n, 3)))
            q_samples = q_samples.at[:, 2].add(1.0)  # 速度偏移
            
            mmd_val, _ = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, self.key)
            mmd_estimates.append(float(mmd_val))
        
        # 验证收敛趋势（更大的样本应该有更稳定的估计）
        # 这里我们检查最后几个估计值的方差是否减小
        early_var = np.var(mmd_estimates[:3])
        late_var = np.var(mmd_estimates[-3:])
        
        print(f"✓ 样本量收敛测试 - 样本量: {sample_sizes}")
        print(f"  MMD估计: {[f'{x:.4f}' for x in mmd_estimates]}")
        print(f"  早期方差: {early_var:.6f}, 后期方差: {late_var:.6f}")
        
        # 不要求严格单调，检查估计值的有限性和合理性
        assert all(jnp.isfinite(x) for x in mmd_estimates), "所有MMD估计应为有限值"
        assert all(abs(x) < 10.0 for x in mmd_estimates), "所有MMD估计的绝对值应合理"
    
    def test_kernel_parameter_sensitivity(self):
        """测试核参数对MMD的敏感性"""
        key1, key2 = jax.random.split(self.key, 2)
        n = 30
        
        # 生成测试数据 - 主要差异在角度上
        p_angles = jax.random.normal(key1, (n,)) * 0.1  # 集中分布
        p_omegas = jax.random.normal(key1, (n,))
        
        q_angles = jax.random.uniform(key2, (n,)) * 2 * jnp.pi  # 均匀分布
        q_omegas = jax.random.normal(key2, (n,))
        
        p_samples = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(p_angles, p_omegas)
        
        q_samples = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(q_angles, q_omegas)
        
        # 测试不同的κ值
        kappa_values = [0.1, 1.0, 5.0, 10.0]
        kappa_mmds = []
        
        for kappa in kappa_values:
            mmd_k = ManifoldMMDLoss(
                manifold=self.manifold,
                kappa=kappa,
                sigma_rbf=1.0
            )
            mmd_val, _ = mmd_k.compute_mmd_squared(p_samples, q_samples, self.key)
            kappa_mmds.append(float(mmd_val))
        
        # κ越大，对角度差异越敏感，MMD应该增大
        assert all(kappa_mmds[i] <= kappa_mmds[i+1] for i in range(len(kappa_mmds)-1)), \
            f"κ增大时MMD应单调增加: {kappa_mmds}"
        
        # 测试不同的σ值（生成速度差异明显的数据）
        p_omegas_diff = jax.random.normal(key1, (n,)) * 0.1  # 小方差
        q_omegas_diff = jax.random.normal(key2, (n,)) * 2.0  # 大方差
        
        p_samples_omega = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(p_angles, p_omegas_diff)
        
        q_samples_omega = jax.vmap(
            lambda t, w: self.manifold.from_angle_velocity(t, w)
        )(q_angles, q_omegas_diff)
        
        sigma_values = [0.1, 0.5, 1.0, 2.0]
        sigma_mmds = []
        
        for sigma in sigma_values:
            mmd_s = ManifoldMMDLoss(
                manifold=self.manifold,
                kappa=1.0,
                sigma_rbf=sigma
            )
            mmd_val, _ = mmd_s.compute_mmd_squared(p_samples_omega, q_samples_omega, self.key)
            sigma_mmds.append(float(mmd_val))
        
        # σ越小，对速度差异越敏感，但这个关系可能不严格单调
        # 我们只验证合理性
        assert all(x > 0 for x in sigma_mmds), "所有σ值的MMD应为正"
        
        print(f"✓ 核参数敏感性测试通过:")
        print(f"  κ参数 {kappa_values}: {[f'{x:.4f}' for x in kappa_mmds]}")
        print(f"  σ参数 {sigma_values}: {[f'{x:.4f}' for x in sigma_mmds]}")


class TestMMDCornerCases:
    """MMD边界情况和极端测试"""
    
    def setup_method(self):
        """测试设置"""
        self.manifold = CylindricalManifold(beta=1.0)
        self.mmd_loss = ManifoldMMDLoss(
            manifold=self.manifold,
            kappa=1.0,
            sigma_rbf=1.0
        )
        self.key = jax.random.PRNGKey(42)
    
    def test_identical_distributions_precision(self):
        """测试相同分布的高精度自相关性"""
        # 使用相同的随机种子生成完全相同的分布
        key_same = jax.random.PRNGKey(12345)
        n = 30
        
        samples = self.manifold.project(jax.random.normal(key_same, (n, 3)))
        
        # 自相关MMD的期望值为0，但单次估计可能为负
        mmd_self, diag = self.mmd_loss.compute_mmd_squared(samples, samples, self.key)
        
        assert jnp.isfinite(mmd_self), f"自相关MMD应为有限值，得到: {mmd_self}"
        assert abs(mmd_self) < 1.0, f"自相关MMD绝对值应合理，得到: {mmd_self}"
        
        # 检查诊断信息的一致性
        assert abs(diag["pp_term"] - diag["qq_term"]) < 1e-12, \
            f"相同分布的pp_term和qq_term应相等: pp={diag['pp_term']}, qq={diag['qq_term']}"
        
        print(f"✓ 高精度自相关测试通过 - MMD: {mmd_self:.2e}")
    
    def test_orthogonal_distributions(self):
        """测试正交分布的MMD"""
        n = 40
        
        # 分布1：θ=0附近，ω=0附近
        p_samples = self.manifold.from_angle_velocity(
            jax.random.normal(self.key, (n,)) * 0.1,
            jax.random.normal(self.key, (n,)) * 0.1
        )
        
        # 分布2：θ=π附近，ω=0附近（角度上正交）
        q_samples = self.manifold.from_angle_velocity(
            jax.random.normal(self.key, (n,)) * 0.1 + jnp.pi,
            jax.random.normal(self.key, (n,)) * 0.1
        )
        
        mmd_orthogonal, _ = self.mmd_loss.compute_mmd_squared(p_samples, q_samples, self.key)
        
        # 正交分布的MMD应该显著大于0
        assert mmd_orthogonal > 0.1, f"正交分布MMD应显著大于0，得到: {mmd_orthogonal}"
        
        print(f"✓ 正交分布测试通过 - MMD: {mmd_orthogonal:.4f}")
    
    def test_extreme_kernel_parameters(self):
        """测试极端核参数的稳定性"""
        key1, key2 = jax.random.split(self.key, 2)
        p_samples = self.manifold.project(jax.random.normal(key1, (20, 3)))
        q_samples = self.manifold.project(jax.random.normal(key2, (20, 3)))
        
        extreme_cases = [
            ("极小κ", {"kappa": 1e-6, "sigma_rbf": 1.0}),
            ("极大κ", {"kappa": 50.0, "sigma_rbf": 1.0}),
            ("极小σ", {"kappa": 1.0, "sigma_rbf": 1e-3}),
            ("极大σ", {"kappa": 1.0, "sigma_rbf": 10.0}),
        ]
        
        for name, params in extreme_cases:
            mmd_extreme = ManifoldMMDLoss(manifold=self.manifold, **params)
            
            try:
                mmd_val, _ = mmd_extreme.compute_mmd_squared(p_samples, q_samples, self.key)
                
                assert jnp.isfinite(mmd_val), f"{name}: MMD应为有限值，得到: {mmd_val}"
                
                print(f"✓ {name}测试通过 - MMD: {mmd_val:.6f}")
                
            except Exception as e:
                pytest.fail(f"{name}测试失败: {e}")
    
    def test_mixed_sample_sizes(self):
        """测试不同样本大小的混合"""
        key1, key2 = jax.random.split(self.key, 2)
        
        # 不同大小的样本集
        small_samples = self.manifold.project(jax.random.normal(key1, (5, 3)))
        large_samples = self.manifold.project(jax.random.normal(key2, (50, 3)))
        
        # 小对大
        mmd_small_large, diag1 = self.mmd_loss.compute_mmd_squared(
            small_samples, large_samples, self.key
        )
        
        # 大对小（应该对称）
        mmd_large_small, diag2 = self.mmd_loss.compute_mmd_squared(
            large_samples, small_samples, self.key
        )
        
        # 验证对称性
        symmetry_error = abs(mmd_small_large - mmd_large_small)
        assert symmetry_error < 1e-12, f"混合样本大小对称性误差: {symmetry_error}"
        
        # 验证有效样本数记录正确
        assert diag1["effective_n"] == 5.0, f"有效样本数n错误: {diag1['effective_n']}"
        assert diag1["effective_m"] == 50.0, f"有效样本数m错误: {diag1['effective_m']}"
        
        print(f"✓ 混合样本大小测试通过 - MMD: {mmd_small_large:.6f}")


# ===== 性能基准测试 =====

class TestMMDPerformance:
    """MMD性能基准测试"""
    
    def setup_method(self):
        """测试设置"""
        self.manifold = CylindricalManifold(beta=1.0)
        self.mmd_loss = ManifoldMMDLoss(
            manifold=self.manifold,
            kappa=1.0,
            sigma_rbf=1.0
        )
        
    def test_large_scale_stability(self):
        """测试大规模数据的稳定性"""
        # 大样本测试
        n_large = 1000
        key = jax.random.PRNGKey(42)
        key1, key2 = jax.random.split(key, 2)
        
        large_p = self.manifold.project(jax.random.normal(key1, (n_large, 3)))
        large_q = self.manifold.project(jax.random.normal(key2, (n_large, 3)))
        
        # 性能计时（简单版本）
        import time
        start_time = time.time()
        
        mmd_large, diag_large = self.mmd_loss.compute_mmd_squared(large_p, large_q, key)
        
        end_time = time.time()
        computation_time = end_time - start_time
        
        # 验证结果有效性
        assert jnp.isfinite(mmd_large), f"大规模MMD应为有限值，得到: {mmd_large}"
        
        # 验证诊断信息完整性
        required_keys = ["pp_term", "qq_term", "pq_term", "effective_n", "effective_m"]
        for key_name in required_keys:
            assert key_name in diag_large, f"缺少诊断键: {key_name}"
            assert jnp.isfinite(diag_large[key_name]), f"诊断值{key_name}应为有限值"
        
        print(f"✓ 大规模稳定性测试通过 - n={n_large}, MMD: {mmd_large:.6f}, 时间: {computation_time:.3f}s")
    
    def test_memory_efficiency(self):
        """测试内存使用效率"""
        # 这是一个简单的内存测试，验证没有明显的内存泄漏
        key = jax.random.PRNGKey(42)
        
        # 连续多次计算
        for i in range(10):
            key_i = jax.random.PRNGKey(i)
            key1, key2 = jax.random.split(key_i, 2)
            
            samples_p = self.manifold.project(jax.random.normal(key1, (100, 3)))
            samples_q = self.manifold.project(jax.random.normal(key2, (100, 3)))
            
            mmd_val, _ = self.mmd_loss.compute_mmd_squared(samples_p, samples_q, key)
            
            # 基本有效性检查
            assert jnp.isfinite(mmd_val), f"迭代{i}: MMD应为有限值"
        
        print(f"✓ 内存效率测试通过 - 10次迭代无异常")


if __name__ == "__main__":
    # 运行特定测试组
    print("=== MMSBVI ManifoldMMDLoss 测试套件 ===")
    print("🎯 开始流形MMD损失的严格数学验证...")
    
    # 可以在这里添加快速验证脚本
    manifold = CylindricalManifold(beta=1.0)
    mmd_loss = ManifoldMMDLoss(manifold=manifold, kappa=1.0, sigma_rbf=1.0)
    
    key = jax.random.PRNGKey(42)
    key1, key2 = jax.random.split(key, 2)
    
    p_samples = manifold.project(jax.random.normal(key1, (10, 3)))
    q_samples = manifold.project(jax.random.normal(key2, (10, 3)))
    
    mmd, diag = mmd_loss.compute_mmd_squared(p_samples, q_samples, key)
    
    print(f"快速验证 - MMD: {mmd:.6f}")
    print(f"诊断信息键: {list(diag.keys())}")
    print("✅ 基础功能正常，准备运行完整测试套件...")
    print("\n运行命令: pytest tests/test_mmd.py -v --tb=short")