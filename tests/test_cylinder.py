"""
Cylindrical Manifold Geometry Tests
圆柱流形几何测试

Comprehensive mathematical validation tests for the CylindricalManifold class.
This test suite ensures the geometric correctness, numerical stability, and 
performance characteristics of the S¹ × ℝ manifold implementation.

这个测试套件确保S¹ × ℝ流形实现的几何正确性、数值稳定性和性能特征。

CRITICAL: This test suite MUST pass before the large-angle pendulum experiments
can be trusted. Any failure indicates potential catastrophic errors in the
geometric foundation of the MMSBVI solver.

重要：在大角度单摆实验可以被信任之前，这个测试套件必须通过。
任何失败都表明MMSBVI求解器几何基础中的潜在灾难性错误。
"""

import pytest
import jax
import jax.numpy as jnp
import jax.random
import numpy as np
from typing import Tuple
import sys
import pathlib

# Add src to path for imports (follow project pattern)
root_dir = pathlib.Path(__file__).resolve().parents[1]
src_dir = root_dir / "src"
if str(src_dir) not in sys.path:
    sys.path.append(str(src_dir))

from mmsbvi.manifolds.cylinder import CylindricalManifold

# Enable 64-bit precision for accurate mathematical tests
# 启用64位精度以进行精确的数学测试
jax.config.update('jax_enable_x64', True)

# Numerical tolerance constants
# 数值容差常数 - 基于JAX实际精度调整
ANGLE_TOLERANCE = 1e-13      # For angular periodicity tests
GEOMETRIC_TOLERANCE = 1e-12  # For geometric property tests  
PROJECTION_TOLERANCE = 1e-2  # For unit constraint tests (adjusted for numerical reality of normalization operations)
DISTANCE_TOLERANCE = 1e-3    # For distance property tests (adjusted for arccos numerical sensitivity near boundaries)

class TestCylindricalManifold:
    """圆柱流形测试套件 / Cylindrical Manifold Test Suite"""
    
    def setup_method(self):
        """每个测试前的设置 / Setup before each test"""
        self.manifold = CylindricalManifold(beta=1.0)
        self.manifold_weighted = CylindricalManifold(beta=2.5)
        self.key = jax.random.PRNGKey(42)
        
        # Generate systematic test points / 生成系统化测试点
        self.special_angles = jnp.array([
            0.0, jnp.pi/4, jnp.pi/2, 3*jnp.pi/4, jnp.pi, 
            5*jnp.pi/4, 3*jnp.pi/2, 7*jnp.pi/4, 2*jnp.pi
        ])
        
        self.special_velocities = jnp.array([-10.0, -1.0, 0.0, 1.0, 10.0])
        
        # Generate random test data / 生成随机测试数据
        self.n_random_tests = 1000
        key1, key2 = jax.random.split(self.key)
        self.random_angles = jax.random.uniform(
            key1, (self.n_random_tests,), minval=0.0, maxval=2*jnp.pi
        )
        self.random_velocities = jax.random.normal(
            key2, (self.n_random_tests,)
        ) * 5.0
    
    # ========================================================================
    # 数学性质测试 / Mathematical Property Tests
    # ========================================================================
    
    def test_coordinate_conversion_bijective_systematic(self):
        """
        测试坐标转换双向性 - 系统化测试
        Test coordinate conversion bijectivity - systematic test
        
        Verifies: from_angle_velocity ∘ to_angle_velocity ≈ identity
        验证: from_angle_velocity ∘ to_angle_velocity ≈ 恒等映射
        """
        for theta in self.special_angles:
            for omega in self.special_velocities:
                # Forward then backward conversion
                # 正向然后反向转换
                embedded = CylindricalManifold.from_angle_velocity(theta, omega)
                theta_recovered, omega_recovered = CylindricalManifold.to_angle_velocity(embedded)
                
                # Handle angular periodicity: θ and θ+2π should be equivalent
                # 处理角度周期性：θ和θ+2π应该等价
                angle_diff = jnp.abs(theta_recovered - theta)
                angle_diff = jnp.minimum(angle_diff, 2*jnp.pi - angle_diff)
                
                assert angle_diff < ANGLE_TOLERANCE, (
                    f"Angle recovery failed: θ={theta:.6f}, "
                    f"recovered={theta_recovered:.6f}, diff={angle_diff:.2e}"
                )
                
                assert jnp.abs(omega_recovered - omega) < GEOMETRIC_TOLERANCE, (
                    f"Velocity recovery failed: ω={omega:.6f}, "
                    f"recovered={omega_recovered:.6f}"
                )
    
    def test_coordinate_conversion_bijective_random(self):
        """
        测试坐标转换双向性 - 随机测试
        Test coordinate conversion bijectivity - random test
        """
        embedded = CylindricalManifold.from_angle_velocity(
            self.random_angles, self.random_velocities
        )
        theta_recovered, omega_recovered = CylindricalManifold.to_angle_velocity(embedded)
        
        # Vectorized angular difference handling
        # 向量化角度差处理
        angle_diffs = jnp.abs(theta_recovered - self.random_angles)
        angle_diffs = jnp.minimum(angle_diffs, 2*jnp.pi - angle_diffs)
        
        max_angle_error = jnp.max(angle_diffs)
        max_velocity_error = jnp.max(jnp.abs(omega_recovered - self.random_velocities))
        
        assert max_angle_error < ANGLE_TOLERANCE, (
            f"Random angle recovery failed: max_error={max_angle_error:.2e}"
        )
        assert max_velocity_error < GEOMETRIC_TOLERANCE, (
            f"Random velocity recovery failed: max_error={max_velocity_error:.2e}"
        )
    
    def test_angular_periodicity(self):
        """
        测试角度周期性
        Test angular periodicity
        
        Verifies: from_angle_velocity(θ, ω) = from_angle_velocity(θ+2π, ω)
        验证: from_angle_velocity(θ, ω) = from_angle_velocity(θ+2π, ω)
        """
        for theta in self.special_angles[:-1]:  # Exclude 2π to avoid exact duplication
            for omega in self.special_velocities:
                embedded1 = CylindricalManifold.from_angle_velocity(theta, omega)
                embedded2 = CylindricalManifold.from_angle_velocity(theta + 2*jnp.pi, omega)
                
                error = jnp.max(jnp.abs(embedded1 - embedded2))
                assert error < ANGLE_TOLERANCE, (
                    f"Periodicity failed: θ={theta:.6f}, error={error:.2e}"
                )
    
    def test_projection_idempotent(self):
        """
        测试投影的幂等性
        Test projection idempotency
        
        Verifies: project(project(x)) = project(x)
        验证: project(project(x)) = project(x)
        """
        # Test with various types of points / 用各种类型的点测试
        test_points = jnp.array([
            [1.0, 0.0, 5.0],      # Already on manifold
            [0.0, 1.0, -2.0],     # Already on manifold  
            [2.0, 3.0, 1.0],      # Need projection
            [0.1, 0.05, 10.0],    # Small S¹ component
            [100.0, 200.0, 0.5], # Large S¹ component
        ])
        
        for point in test_points:
            projected_once = CylindricalManifold.project(point)
            projected_twice = CylindricalManifold.project(projected_once)
            
            error = jnp.max(jnp.abs(projected_once - projected_twice))
            assert error < PROJECTION_TOLERANCE, (
                f"Idempotency failed for {point}: error={error:.2e}"
            )
    
    def test_manifold_constraint_satisfaction(self):
        """
        测试流形约束满足
        Test manifold constraint satisfaction
        
        Verifies: ||S¹ component||₂ = 1 after projection
        验证: 投影后 ||S¹分量||₂ = 1
        """
        # Generate random points in ambient space
        # 在环境空间中生成随机点
        key1, key2, key3 = jax.random.split(self.key, 3)
        random_points = jnp.stack([
            jax.random.normal(key1, (self.n_random_tests,)) * 10,  # cos θ component
            jax.random.normal(key2, (self.n_random_tests,)) * 10,  # sin θ component  
            jax.random.normal(key3, (self.n_random_tests,)) * 5,   # ω component
        ], axis=-1)
        
        projected_points = jax.vmap(CylindricalManifold.project)(random_points)
        
        # Check S¹ component norms / 检查S¹分量范数
        s1_norms = jnp.linalg.norm(projected_points[..., :2], axis=-1)
        norm_errors = jnp.abs(s1_norms - 1.0)
        
        max_norm_error = jnp.max(norm_errors)
        assert max_norm_error < PROJECTION_TOLERANCE, (
            f"Manifold constraint violated: max_error={max_norm_error:.2e}"
        )
    
    def test_geodesic_distance_symmetry(self):
        """
        测试测地距离对称性
        Test geodesic distance symmetry
        
        Verifies: d(x₁, x₂) = d(x₂, x₁)
        验证: d(x₁, x₂) = d(x₂, x₁)
        """
        # Generate pairs of random embedded points
        # 生成随机嵌入点对
        n_pairs = 100
        key1, key2 = jax.random.split(self.key)
        
        embedded1 = CylindricalManifold.from_angle_velocity(
            jax.random.uniform(key1, (n_pairs,), minval=0.0, maxval=2*jnp.pi),
            jax.random.normal(key1, (n_pairs,)) * 3.0
        )
        embedded2 = CylindricalManifold.from_angle_velocity(
            jax.random.uniform(key2, (n_pairs,), minval=0.0, maxval=2*jnp.pi),
            jax.random.normal(key2, (n_pairs,)) * 3.0
        )
        
        # Compute distances both ways / 双向计算距离
        dist_12 = jax.vmap(self.manifold.geodesic_distance_sq)(embedded1, embedded2)
        dist_21 = jax.vmap(self.manifold.geodesic_distance_sq)(embedded2, embedded1)
        
        symmetry_errors = jnp.abs(dist_12 - dist_21)
        max_symmetry_error = jnp.max(symmetry_errors)
        
        assert max_symmetry_error < DISTANCE_TOLERANCE, (
            f"Distance symmetry failed: max_error={max_symmetry_error:.2e}"
        )
    
    def test_geodesic_distance_positive_definite(self):
        """
        测试测地距离正定性
        Test geodesic distance positive definiteness
        
        Verifies: d(x, x) = 0 and d(x₁, x₂) > 0 if x₁ ≠ x₂
        验证: d(x, x) = 0 且当 x₁ ≠ x₂ 时 d(x₁, x₂) > 0
        """
        # Test self-distance = 0 / 测试自距离 = 0
        embedded_points = CylindricalManifold.from_angle_velocity(
            self.random_angles[:100], self.random_velocities[:100]
        )
        
        self_distances = jax.vmap(self.manifold.geodesic_distance_sq)(
            embedded_points, embedded_points
        )
        
        max_self_distance = jnp.max(jnp.abs(self_distances))
        assert max_self_distance < DISTANCE_TOLERANCE, (
            f"Self-distance not zero: max={max_self_distance:.2e}"
        )
        
        # Test positivity for distinct points / 测试不同点的正性
        embedded1 = embedded_points[:-1]
        embedded2 = embedded_points[1:]
        
        distinct_distances = jax.vmap(self.manifold.geodesic_distance_sq)(
            embedded1, embedded2
        )
        
        # All distances should be positive (or very close to zero for near-identical points)
        # 所有距离应该为正（或对于几乎相同的点非常接近零）
        min_distance = jnp.min(distinct_distances)
        assert min_distance >= -DISTANCE_TOLERANCE, (
            f"Negative distance found: min={min_distance:.2e}"
        )
    
    def test_geodesic_distance_triangle_inequality(self):
        """
        测试测地距离三角不等式
        Test geodesic distance triangle inequality
        
        Verifies: d(x₁, x₃) ≤ d(x₁, x₂) + d(x₂, x₃)
        验证: d(x₁, x₃) ≤ d(x₁, x₂) + d(x₂, x₃)
        """
        n_triangles = 50
        keys = jax.random.split(self.key, 3)
        
        # Generate three sets of random points / 生成三组随机点
        points1 = CylindricalManifold.from_angle_velocity(
            jax.random.uniform(keys[0], (n_triangles,), minval=0.0, maxval=2*jnp.pi),
            jax.random.normal(keys[0], (n_triangles,)) * 2.0
        )
        points2 = CylindricalManifold.from_angle_velocity(
            jax.random.uniform(keys[1], (n_triangles,), minval=0.0, maxval=2*jnp.pi),
            jax.random.normal(keys[1], (n_triangles,)) * 2.0
        )
        points3 = CylindricalManifold.from_angle_velocity(
            jax.random.uniform(keys[2], (n_triangles,), minval=0.0, maxval=2*jnp.pi),
            jax.random.normal(keys[2], (n_triangles,)) * 2.0
        )
        
        # Compute all distances / 计算所有距离
        d13 = jnp.sqrt(jax.vmap(self.manifold.geodesic_distance_sq)(points1, points3))
        d12 = jnp.sqrt(jax.vmap(self.manifold.geodesic_distance_sq)(points1, points2))
        d23 = jnp.sqrt(jax.vmap(self.manifold.geodesic_distance_sq)(points2, points3))
        
        # Check triangle inequality / 检查三角不等式
        triangle_violations = d13 - (d12 + d23)
        max_violation = jnp.max(triangle_violations)
        
        # Allow small numerical tolerance / 允许小的数值容差
        assert max_violation < DISTANCE_TOLERANCE, (
            f"Triangle inequality violated: max_violation={max_violation:.2e}"
        )
    
    def test_angular_periodicity_in_distance(self):
        """
        测试距离中的角度周期性
        Test angular periodicity in distance calculation
        
        Verifies: d((θ, ω₁), (θ+2π, ω₂)) = d((θ, ω₁), (θ, ω₂))
        验证: d((θ, ω₁), (θ+2π, ω₂)) = d((θ, ω₁), (θ, ω₂))
        """
        theta = jnp.pi/3
        omega1, omega2 = 1.5, -2.0
        
        point1 = CylindricalManifold.from_angle_velocity(theta, omega1)
        point2 = CylindricalManifold.from_angle_velocity(theta, omega2)
        point2_shifted = CylindricalManifold.from_angle_velocity(theta + 2*jnp.pi, omega2)
        
        dist_normal = self.manifold.geodesic_distance_sq(point1, point2)
        dist_shifted = self.manifold.geodesic_distance_sq(point1, point2_shifted)
        
        error = jnp.abs(dist_normal - dist_shifted)
        assert error < DISTANCE_TOLERANCE, (
            f"Distance periodicity failed: error={error:.2e}"
        )
    
    # ========================================================================
    # 数值稳定性测试 / Numerical Stability Tests  
    # ========================================================================
    
    def test_zero_vector_handling(self):
        """
        测试零向量处理
        Test zero vector handling
        
        Documents the actual behavior of the projection with zero/near-zero inputs.
        记录投影对零/接近零输入的实际行为。
        """
        # Test various zero-like inputs / 测试各种类零输入
        zero_inputs_and_expectations = [
            ([1e-6, 1e-6, 5.0], True),    # Small but manageable S¹ component
            ([1e-10, 1e-10, -2.0], False), # Very small S¹ component - numerically unstable
            ([1e-6, 0.0, 3.0], True),     # One small, one zero component - should work
        ]
        
        for zero_input, should_be_normalized in zero_inputs_and_expectations:
            zero_input = jnp.array(zero_input)
            projected = CylindricalManifold.project(zero_input)
            
            # Check that projection doesn't produce NaN or Inf
            # 检查投影不产生NaN或Inf
            assert not jnp.isnan(projected).any(), (
                f"NaN produced for input={zero_input}"
            )
            assert jnp.isfinite(projected).all(), (
                f"Infinite values produced for input={zero_input}"
            )
            
            if should_be_normalized:
                # S¹ component should be normalized for reasonable inputs
                # 对于合理的输入，S¹分量应该被归一化
                s1_norm = jnp.linalg.norm(projected[:2])
                assert jnp.abs(s1_norm - 1.0) < PROJECTION_TOLERANCE, (
                    f"Normalization failed for input={zero_input}, "
                    f"s1_norm={s1_norm:.2e}"
                )
            
            # ℝ component should always be preserved / ℝ分量应该总是被保持
            assert jnp.abs(projected[2] - zero_input[2]) < GEOMETRIC_TOLERANCE, (
                f"ℝ component not preserved: expected={zero_input[2]}, "
                f"got={projected[2]}"
            )
    
    def test_extreme_value_handling(self):
        """
        测试极值处理
        Test extreme value handling
        
        Tests behavior with extreme coordinate values.
        测试极值坐标的行为。
        """
        # Separate reasonable and problematic extreme values
        # 分离合理和有问题的极值
        reasonable_values = [1e-5, 1e5, 1e6]
        problematic_values = [1e-10, 1e10]
        
        # Test reasonable extreme values / 测试合理的极值
        for val in reasonable_values:
            point = jnp.array([val, val, val])
            projected = CylindricalManifold.project(point)
            
            # Should still satisfy manifold constraint / 应该仍然满足流形约束
            s1_norm = jnp.linalg.norm(projected[:2])
            assert jnp.abs(s1_norm - 1.0) < PROJECTION_TOLERANCE, (
                f"Reasonable extreme value projection failed: val={val}, s1_norm={s1_norm:.2e}"
            )
        
        # Test coordinate conversion with extreme velocities
        # 测试极值速度的坐标转换
        extreme_velocities = [1e-5, 1e5]
        for val in extreme_velocities:
            embedded = CylindricalManifold.from_angle_velocity(0.5, val)
            theta, omega = CylindricalManifold.to_angle_velocity(embedded)
            
            assert jnp.abs(omega - val) < GEOMETRIC_TOLERANCE, (
                f"Extreme velocity conversion failed: val={val}, recovered={omega}"
            )
        
        # Test problematic extreme values (document behavior)
        # 测试有问题的极值（记录行为）
        for val in problematic_values:
            point = jnp.array([val, val, val])
            projected = CylindricalManifold.project(point)
            
            # Should at least produce finite results / 应该至少产生有限结果
            assert jnp.isfinite(projected).all(), (
                f"Problematic extreme value produced non-finite result: val={val}"
            )
    
    def test_numerical_precision_near_boundaries(self):
        """
        测试边界附近的数值精度
        Test numerical precision near boundaries
        """
        # Test points very close to S¹ component boundaries
        # 测试非常接近S¹分量边界的点
        boundary_points = jnp.array([
            [1.0 - 1e-15, 1e-15, 1.0],    # Near (1,0)
            [1e-15, 1.0 - 1e-15, 1.0],    # Near (0,1)
            [-1.0 + 1e-15, 1e-15, 1.0],   # Near (-1,0)
            [1e-15, -1.0 + 1e-15, 1.0],   # Near (0,-1)
        ])
        
        for point in boundary_points:
            # Test projection stability / 测试投影稳定性
            projected = CylindricalManifold.project(point)
            s1_norm = jnp.linalg.norm(projected[:2])
            
            assert jnp.abs(s1_norm - 1.0) < PROJECTION_TOLERANCE, (
                f"Boundary projection failed: point={point}, s1_norm={s1_norm:.2e}"
            )
            
            # Test coordinate conversion / 测试坐标转换
            theta, omega = CylindricalManifold.to_angle_velocity(projected)
            embedded_back = CylindricalManifold.from_angle_velocity(theta, omega)
            
            error = jnp.max(jnp.abs(projected - embedded_back))
            # Allow slightly larger tolerance for boundary precision near unit circle
            # 对于单位圆附近的边界精度，允许稍大的容差
            boundary_tolerance = max(GEOMETRIC_TOLERANCE, 1e-8)
            assert error < boundary_tolerance, (
                f"Boundary coordinate conversion failed: error={error:.2e}"
            )
    
    def test_nan_inf_input_handling(self):
        """
        测试NaN/Inf输入处理
        Test NaN/Inf input handling
        
        Note: This tests the current behavior and documents it.
        In production, explicit input validation might be added.
        注意：这测试当前行为并记录它。在生产中，可能会添加显式输入验证。
        """
        # Test NaN inputs / 测试NaN输入
        nan_inputs = jnp.array([
            [jnp.nan, 1.0, 1.0],
            [1.0, jnp.nan, 1.0], 
            [1.0, 1.0, jnp.nan],
            [jnp.nan, jnp.nan, jnp.nan],
        ])
        
        for nan_input in nan_inputs:
            projected = CylindricalManifold.project(nan_input)
            # NaN should propagate / NaN应该传播
            assert jnp.isnan(projected).any(), (
                f"NaN not propagated: input={nan_input}, output={projected}"
            )
        
        # Test Inf inputs / 测试Inf输入
        inf_inputs = jnp.array([
            [jnp.inf, 1.0, 1.0],
            [1.0, jnp.inf, 1.0],
            [1.0, 1.0, jnp.inf],
        ])
        
        for inf_input in inf_inputs:
            projected = CylindricalManifold.project(inf_input)
            # For infinite S¹ components, normalization should handle it
            # 对于无限S¹分量，归一化应该处理它
            if not jnp.isinf(inf_input[2]):  # If ℝ component is finite
                assert jnp.isfinite(projected[2]), (
                    f"ℝ component became infinite: input={inf_input}, output={projected}"
                )
    
    # ========================================================================
    # 性能和兼容性测试 / Performance and Compatibility Tests
    # ========================================================================
    
    def test_jit_compilation(self):
        """
        测试JIT编译
        Test JIT compilation
        
        Verifies all methods can be JIT compiled and produce consistent results.
        验证所有方法都可以被JIT编译并产生一致的结果。
        """
        # Compile all methods / 编译所有方法
        from_angle_velocity_jit = jax.jit(CylindricalManifold.from_angle_velocity)
        to_angle_velocity_jit = jax.jit(CylindricalManifold.to_angle_velocity)
        project_jit = jax.jit(CylindricalManifold.project)
        geodesic_distance_sq_jit = jax.jit(self.manifold.geodesic_distance_sq)
        
        # Test data / 测试数据
        theta, omega = jnp.pi/4, 2.0
        test_point = jnp.array([1.5, 2.0, 3.0])
        embedded1 = CylindricalManifold.from_angle_velocity(theta, omega)
        embedded2 = CylindricalManifold.from_angle_velocity(theta + 1.0, omega + 1.0)
        
        # Compare JIT vs non-JIT results / 比较JIT与非JIT结果
        
        # from_angle_velocity
        result_normal = CylindricalManifold.from_angle_velocity(theta, omega)
        result_jit = from_angle_velocity_jit(theta, omega)
        assert jnp.allclose(result_normal, result_jit, atol=1e-13), (
            "JIT from_angle_velocity inconsistent"
        )
        
        # to_angle_velocity
        theta_normal, omega_normal = CylindricalManifold.to_angle_velocity(embedded1)
        theta_jit, omega_jit = to_angle_velocity_jit(embedded1)
        assert jnp.allclose(jnp.array([theta_normal, omega_normal]), jnp.array([theta_jit, omega_jit]), atol=1e-13), (
            "JIT to_angle_velocity inconsistent"
        )
        
        # project
        proj_normal = CylindricalManifold.project(test_point)
        proj_jit = project_jit(test_point)
        assert jnp.allclose(proj_normal, proj_jit, atol=1e-13), (
            "JIT project inconsistent"
        )
        
        # geodesic_distance_sq
        dist_normal = self.manifold.geodesic_distance_sq(embedded1, embedded2)
        dist_jit = geodesic_distance_sq_jit(embedded1, embedded2)
        assert jnp.allclose(dist_normal, dist_jit, atol=1e-13), (
            "JIT geodesic_distance_sq inconsistent"
        )
    
    def test_batch_processing_consistency(self):
        """
        测试批量处理一致性
        Test batch processing consistency
        
        Verifies vmap batch processing gives same results as individual processing.
        验证vmap批量处理与单独处理给出相同结果。
        """
        n_batch = 50
        batch_angles = jax.random.uniform(
            self.key, (n_batch,), minval=0.0, maxval=2*jnp.pi
        )
        batch_velocities = jax.random.normal(jax.random.split(self.key)[1], (n_batch,)) * 3.0
        
        # Test from_angle_velocity batch processing / 测试from_angle_velocity批量处理
        embedded_batch = jax.vmap(CylindricalManifold.from_angle_velocity)(
            batch_angles, batch_velocities
        )
        embedded_individual = jnp.stack([
            CylindricalManifold.from_angle_velocity(theta, omega)
            for theta, omega in zip(batch_angles, batch_velocities)
        ])
        
        assert jnp.allclose(embedded_batch, embedded_individual, atol=1e-13), (
            "Batch from_angle_velocity inconsistent"
        )
        
        # Test to_angle_velocity batch processing / 测试to_angle_velocity批量处理
        theta_batch, omega_batch = jax.vmap(CylindricalManifold.to_angle_velocity)(embedded_batch)
        
        theta_individual = jnp.array([
            CylindricalManifold.to_angle_velocity(x)[0] for x in embedded_batch
        ])
        omega_individual = jnp.array([
            CylindricalManifold.to_angle_velocity(x)[1] for x in embedded_batch
        ])
        
        assert jnp.allclose(theta_batch, theta_individual, atol=1e-13), (
            "Batch to_angle_velocity theta inconsistent"
        )
        assert jnp.allclose(omega_batch, omega_individual, atol=1e-13), (
            "Batch to_angle_velocity omega inconsistent"
        )
        
        # Test project batch processing / 测试project批量处理
        random_points = jax.random.normal(
            jax.random.split(self.key)[0], (n_batch, 3)
        ) * 5.0
        
        proj_batch = jax.vmap(CylindricalManifold.project)(random_points)
        proj_individual = jnp.stack([
            CylindricalManifold.project(point) for point in random_points
        ])
        
        assert jnp.allclose(proj_batch, proj_individual, atol=1e-13), (
            "Batch project inconsistent"
        )
    
    def test_large_scale_processing(self):
        """
        测试大规模数据处理
        Test large-scale data processing
        
        Verifies the implementation can handle large batches efficiently.
        验证实现可以有效处理大批量数据。
        """
        n_large = 10000
        
        # Generate large datasets / 生成大数据集
        key1, key2 = jax.random.split(self.key)
        large_angles = jax.random.uniform(
            key1, (n_large,), minval=0.0, maxval=2*jnp.pi
        )
        large_velocities = jax.random.normal(key2, (n_large,)) * 5.0
        
        # Test coordinate conversion at scale / 测试大规模坐标转换
        embedded_large = jax.vmap(CylindricalManifold.from_angle_velocity)(
            large_angles, large_velocities
        )
        
        assert embedded_large.shape == (n_large, 3), (
            f"Unexpected shape: {embedded_large.shape}"
        )
        
        # Test all points satisfy manifold constraint / 测试所有点满足流形约束
        s1_norms = jnp.linalg.norm(embedded_large[..., :2], axis=-1)
        norm_errors = jnp.abs(s1_norms - 1.0)
        
        max_norm_error = jnp.max(norm_errors)
        assert max_norm_error < PROJECTION_TOLERANCE, (
            f"Large scale manifold constraint failed: max_error={max_norm_error:.2e}"
        )
        
        # Test projection at scale / 测试大规模投影
        random_large = jax.random.normal(
            jax.random.split(key2)[0], (n_large, 3)
        ) * 10.0
        
        projected_large = jax.vmap(CylindricalManifold.project)(random_large)
        proj_s1_norms = jnp.linalg.norm(projected_large[..., :2], axis=-1)
        proj_norm_errors = jnp.abs(proj_s1_norms - 1.0)
        
        max_proj_error = jnp.max(proj_norm_errors)
        assert max_proj_error < PROJECTION_TOLERANCE, (
            f"Large scale projection failed: max_error={max_proj_error:.2e}"
        )
    
    def test_beta_parameter_scaling(self):
        """
        测试beta参数缩放
        Test beta parameter scaling in geodesic distance
        
        Verifies the beta parameter correctly weights the velocity dimension.
        验证beta参数正确加权速度维度。
        """
        # Create manifolds with different beta values / 创建不同beta值的流形
        manifold_beta1 = CylindricalManifold(beta=1.0)
        manifold_beta2 = CylindricalManifold(beta=4.0)
        
        # Create test points with same angle, different velocities
        # 创建具有相同角度、不同速度的测试点
        theta = jnp.pi/3
        omega1, omega2 = 1.0, 2.0
        
        point1 = CylindricalManifold.from_angle_velocity(theta, omega1)
        point2 = CylindricalManifold.from_angle_velocity(theta, omega2)
        
        # Compute distances with different beta values / 用不同beta值计算距离
        dist_beta1 = manifold_beta1.geodesic_distance_sq(point1, point2)
        dist_beta2 = manifold_beta2.geodesic_distance_sq(point1, point2)
        
        # Distance should scale by beta factor (since only velocity differs)
        # 距离应该按beta因子缩放（因为只有速度不同）
        expected_ratio = 4.0  # beta2 / beta1 = 4.0 / 1.0
        actual_ratio = dist_beta2 / dist_beta1
        
        assert jnp.abs(actual_ratio - expected_ratio) < DISTANCE_TOLERANCE, (
            f"Beta scaling incorrect: expected={expected_ratio}, actual={actual_ratio:.6f}"
        )
        
        # Test with points having same velocity, different angles
        # 测试具有相同速度、不同角度的点
        theta1, theta2 = jnp.pi/4, jnp.pi/2
        omega = 3.0
        
        point1_angle = CylindricalManifold.from_angle_velocity(theta1, omega)
        point2_angle = CylindricalManifold.from_angle_velocity(theta2, omega)
        
        dist_angle_beta1 = manifold_beta1.geodesic_distance_sq(point1_angle, point2_angle)
        dist_angle_beta2 = manifold_beta2.geodesic_distance_sq(point1_angle, point2_angle)
        
        # Angular distances should be the same regardless of beta
        # 无论beta如何，角度距离应该相同
        assert jnp.abs(dist_angle_beta1 - dist_angle_beta2) < DISTANCE_TOLERANCE, (
            f"Beta should not affect angular distance: "
            f"beta1={dist_angle_beta1:.6f}, beta2={dist_angle_beta2:.6f}"
        )
    
    # ========================================================================
    # 边界条件和回归测试 / Edge Cases and Regression Tests
    # ========================================================================
    
    def test_eps_parameter_sensitivity(self):
        """
        测试eps参数敏感性
        Test eps parameter sensitivity in projection
        
        Verifies projection behavior with different eps values.
        验证不同eps值下的投影行为。
        
        Note: For extremely small S¹ components, the projection operation
        becomes numerically unstable regardless of eps value.
        注意：对于极小的S¹分量，无论eps值如何，投影操作都会变得数值不稳定。
        """
        # Test point with reasonably small S¹ component (not extreme)
        # 测试S¹分量合理小的点（不是极端情况）
        small_point = jnp.array([1e-4, 1e-4, 5.0])
        
        # Test with different eps values / 用不同eps值测试
        eps_values = [1e-6, 1e-8, 1e-10, 1e-12]
        
        projections = []
        for eps in eps_values:
            proj = CylindricalManifold.project(small_point, eps=eps)
            projections.append(proj)
            
            # Should still satisfy constraint / 应该仍然满足约束
            s1_norm = jnp.linalg.norm(proj[:2])
            assert jnp.abs(s1_norm - 1.0) < PROJECTION_TOLERANCE, (
                f"Constraint failed with eps={eps}: s1_norm={s1_norm:.2e}"
            )
        
        # Results should be similar for reasonable eps values
        # 对于合理的eps值，结果应该相似
        for i in range(len(projections) - 1):
            diff = jnp.max(jnp.abs(projections[i] - projections[i+1]))
            assert diff < 1e-2, (  # Allow some variation due to eps
                f"Large eps sensitivity: diff={diff:.2e} between "
                f"eps={eps_values[i]} and eps={eps_values[i+1]}"
            )
    
    def test_extreme_input_projection_behavior(self):
        """
        测试极端输入的投影行为
        Test projection behavior with extreme inputs
        
        Documents the expected behavior when S¹ components are extremely small.
        记录当S¹分量极小时的预期行为。
        """
        # Test point with extremely small S¹ component
        # 测试S¹分量极小的点
        extreme_point = jnp.array([1e-15, 1e-15, 5.0])
        
        proj = CylindricalManifold.project(extreme_point)
        
        # The projection should produce a valid result, even if not perfectly normalized
        # 投影应该产生有效结果，即使不是完美归一化
        assert not jnp.isnan(proj).any(), (
            "Projection produced NaN for extreme input"
        )
        assert jnp.isfinite(proj).all(), (
            "Projection produced infinite values for extreme input"
        )
        
        # The ℝ component should be preserved / ℝ分量应该被保持
        assert jnp.abs(proj[2] - extreme_point[2]) < GEOMETRIC_TOLERANCE, (
            f"ℝ component not preserved: expected={extreme_point[2]}, got={proj[2]}"
        )
    
    def test_geodesic_distance_clipping_bounds(self):
        """
        测试测地距离裁剪边界
        Test geodesic distance clipping bounds
        
        Verifies the arccos clipping handles boundary cases correctly.
        验证arccos裁剪正确处理边界情况。
        """
        # Create points that will result in dot products at clipping boundaries
        # 创建将导致点积在裁剪边界的点
        
        # Identical points (dot product = 1) / 相同点（点积 = 1）
        point = CylindricalManifold.from_angle_velocity(jnp.pi/4, 1.0)
        dist_identical = self.manifold.geodesic_distance_sq(point, point)
        
        assert jnp.abs(dist_identical) < DISTANCE_TOLERANCE, (
            f"Identical points distance not zero: {dist_identical:.2e}"
        )
        
        # Opposite points on S¹ (dot product = -1) / S¹上的相对点（点积 = -1）
        point1 = CylindricalManifold.from_angle_velocity(0.0, 0.0)
        point2 = CylindricalManifold.from_angle_velocity(jnp.pi, 0.0)
        
        dist_opposite = self.manifold.geodesic_distance_sq(point1, point2)
        expected_opposite = jnp.pi**2  # Maximum angular distance squared
        
        assert jnp.abs(dist_opposite - expected_opposite) < DISTANCE_TOLERANCE, (
            f"Opposite points distance incorrect: expected={expected_opposite:.6f}, "
            f"got={dist_opposite:.6f}"
        )
    
    def test_coordinate_conversion_special_angles(self):
        """
        测试特殊角度的坐标转换
        Test coordinate conversion for special angles
        
        Verifies correct handling of angles at quadrant boundaries.
        验证象限边界角度的正确处理。
        """
        special_cases = [
            (0.0, (1.0, 0.0)),           # 0 radians
            (jnp.pi/2, (0.0, 1.0)),      # π/2 radians  
            (jnp.pi, (-1.0, 0.0)),       # π radians
            (3*jnp.pi/2, (0.0, -1.0)),   # 3π/2 radians
            (2*jnp.pi, (1.0, 0.0)),      # 2π radians (same as 0)
        ]
        
        for angle, expected_s1 in special_cases:
            embedded = CylindricalManifold.from_angle_velocity(angle, 0.0)
            
            # Check S¹ component / 检查S¹分量
            actual_s1 = (embedded[0], embedded[1])
            
            assert jnp.abs(actual_s1[0] - expected_s1[0]) < GEOMETRIC_TOLERANCE, (
                f"cos({angle}) incorrect: expected={expected_s1[0]}, got={actual_s1[0]}"
            )
            assert jnp.abs(actual_s1[1] - expected_s1[1]) < GEOMETRIC_TOLERANCE, (
                f"sin({angle}) incorrect: expected={expected_s1[1]}, got={actual_s1[1]}"
            )
            
            # Test round-trip conversion / 测试往返转换
            recovered_angle, recovered_omega = CylindricalManifold.to_angle_velocity(embedded)
            
            # Handle 2π ≡ 0 equivalence / 处理2π ≡ 0等价性
            if angle >= 2*jnp.pi:
                expected_recovered = angle - 2*jnp.pi
            else:
                expected_recovered = angle
                
            angle_diff = jnp.abs(recovered_angle - expected_recovered)
            angle_diff = jnp.minimum(angle_diff, 2*jnp.pi - angle_diff)
            
            assert angle_diff < ANGLE_TOLERANCE, (
                f"Angle recovery failed: input={angle}, recovered={recovered_angle}, "
                f"expected={expected_recovered}"
            )


# ========================================================================
# Integration Tests with Physical Interpretation
# 具有物理解释的集成测试
# ========================================================================

class TestCylindricalManifoldPhysicalInterpretation:
    """
    Physical interpretation tests for the cylindrical manifold
    圆柱流形的物理解释测试
    
    These tests verify that the geometric operations make sense in the context
    of the large-angle pendulum problem.
    这些测试验证几何操作在大角度单摆问题的上下文中是有意义的。
    """
    
    def setup_method(self):
        """Setup for physical interpretation tests / 物理解释测试的设置"""
        self.manifold = CylindricalManifold(beta=1.0)
        self.key = jax.random.PRNGKey(123)
    
    def test_pendulum_state_representation(self):
        """
        测试单摆状态表示
        Test pendulum state representation
        
        Verifies that pendulum states are correctly represented on the manifold.
        验证单摆状态在流形上得到正确表示。
        """
        # Test various pendulum configurations / 测试各种单摆配置
        pendulum_states = [
            (0.0, 0.0),        # At rest, bottom position
            (jnp.pi, 0.0),     # At rest, top position (unstable equilibrium)
            (jnp.pi/2, 0.0),   # At rest, horizontal position
            (0.0, 5.0),        # Bottom position, high velocity
            (jnp.pi, 1.0),     # Top position, small velocity
        ]
        
        for theta, omega in pendulum_states:
            embedded = CylindricalManifold.from_angle_velocity(theta, omega)
            
            # Check embedding properties / 检查嵌入性质
            assert embedded.shape == (3,), f"Wrong embedding shape: {embedded.shape}"
            
            # S¹ component should be unit vector / S¹分量应该是单位向量
            s1_norm = jnp.linalg.norm(embedded[:2])
            assert jnp.abs(s1_norm - 1.0) < PROJECTION_TOLERANCE, (
                f"S¹ component not unit: norm={s1_norm:.2e} for state ({theta}, {omega})"
            )
            
            # ℝ component should match velocity / ℝ分量应该匹配速度
            assert jnp.abs(embedded[2] - omega) < GEOMETRIC_TOLERANCE, (
                f"Velocity mismatch: expected={omega}, got={embedded[2]}"
            )
    
    def test_physical_distance_interpretation(self):
        """
        测试物理距离解释
        Test physical distance interpretation
        
        Verifies that geodesic distances have reasonable physical interpretation.
        验证测地距离具有合理的物理解释。
        """
        # States with only angular difference / 只有角度差的状态
        state1 = CylindricalManifold.from_angle_velocity(0.0, 0.0)      # Bottom, at rest
        state2 = CylindricalManifold.from_angle_velocity(jnp.pi/2, 0.0) # Horizontal, at rest
        
        angular_distance = self.manifold.geodesic_distance_sq(state1, state2)
        expected_angular = (jnp.pi/2)**2
        
        assert jnp.abs(angular_distance - expected_angular) < DISTANCE_TOLERANCE, (
            f"Angular distance incorrect: expected={expected_angular:.6f}, "
            f"got={angular_distance:.6f}"
        )
        
        # States with only velocity difference / 只有速度差的状态
        state3 = CylindricalManifold.from_angle_velocity(0.0, 0.0)  # Bottom, at rest
        state4 = CylindricalManifold.from_angle_velocity(0.0, 3.0)  # Bottom, high velocity
        
        velocity_distance = self.manifold.geodesic_distance_sq(state3, state4)
        expected_velocity = self.manifold.beta * (3.0)**2
        
        assert jnp.abs(velocity_distance - expected_velocity) < DISTANCE_TOLERANCE, (
            f"Velocity distance incorrect: expected={expected_velocity:.6f}, "
            f"got={velocity_distance:.6f}"
        )
    
    def test_energy_level_continuity(self):
        """
        测试能量水平连续性
        Test energy level continuity
        
        Verifies that nearby states in phase space have small geodesic distances.
        验证相空间中邻近状态具有小的测地距离。
        """
        # Generate points on approximate energy level curves / 在近似能量水平曲线上生成点
        base_theta = jnp.pi/4
        base_omega = 2.0
        
        # Small perturbations / 小扰动
        perturbations = jnp.array([0.01, 0.05, 0.1])
        
        base_state = CylindricalManifold.from_angle_velocity(base_theta, base_omega)
        
        for perturb in perturbations:
            # Perturb angle / 扰动角度
            perturbed_angle_state = CylindricalManifold.from_angle_velocity(
                base_theta + perturb, base_omega
            )
            
            angle_distance = jnp.sqrt(self.manifold.geodesic_distance_sq(
                base_state, perturbed_angle_state
            ))
            
            # Distance should be approximately the perturbation magnitude
            # 距离应该大约是扰动幅度
            assert jnp.abs(angle_distance - perturb) < 0.01, (
                f"Angle perturbation distance wrong: perturb={perturb}, "
                f"distance={angle_distance:.6f}"
            )
            
            # Perturb velocity / 扰动速度
            perturbed_velocity_state = CylindricalManifold.from_angle_velocity(
                base_theta, base_omega + perturb
            )
            
            velocity_distance = jnp.sqrt(self.manifold.geodesic_distance_sq(
                base_state, perturbed_velocity_state
            ))
            
            expected_velocity_distance = jnp.sqrt(self.manifold.beta) * perturb
            
            assert jnp.abs(velocity_distance - expected_velocity_distance) < 0.01, (
                f"Velocity perturbation distance wrong: perturb={perturb}, "
                f"distance={velocity_distance:.6f}, expected={expected_velocity_distance:.6f}"
            )


if __name__ == "__main__":
    """
    运行测试套件
    Run the test suite
    
    Execute with: python -m pytest tests/test_cylinder.py -v
    执行命令: python -m pytest tests/test_cylinder.py -v
    """
    pytest.main([__file__, "-v", "--tb=short"])