# Paper and LaTeX files
Paper/
*.tex
*.pdf
*.aux
*.log
*.out
*.toc
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.synctex.gz
*.nav
*.snm
*.vrb

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
offline_wheels/
*.whl
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.conda/
conda-meta/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# JAX compilation cache
jax_cache/

# Experiment outputs and logs
logs/
*.log
experiments/logs/
experiments/results/*.pkl
experiments/results/*.npz

# Temporary files
*.tmp
*.bak
*.backup
.DS_Store
Thumbs.db

# Large data files
*.h5
*.hdf5
*.mat

# Generated figures (keep only key results)
results/pendulum/
results/density_evolution_comparison.png
results/enhanced_rts_academic.png
results/geometric_limits_validation.png
results/method_comparison_dashboard.png
results/parameter_sensitivity_analysis.png
results/rts_equivalence_marginal_driven.png
results/rts_equivalence_observation_driven.png
results/two_marginal_gaussian_results.png

# Keep only the organized validation results
!results/parameter_sensitivity/
!results/geometric_limits/
!results/rts_equivalence/

# Performance profiling
*.prof
*.pstats

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Personal notes and drafts
NOTES.md
TODO.md
DRAFT.md
scratch/