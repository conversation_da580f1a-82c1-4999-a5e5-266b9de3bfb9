import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import FancyArrowPatch
from matplotlib.patches import Circle, Ellipse
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.colors import LinearSegmentedColormap, ListedColormap
import matplotlib.patches as patches
from matplotlib.patches import Arc
from matplotlib.backends.backend_pdf import PdfPages
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import matplotlib.patheffects as path_effects
from matplotlib.patches import PathPatch
import matplotlib.path as mpath

# 设置专业学术风格
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman', 'Times'],
    'font.size': 9,
    'axes.linewidth': 0.8,
    'lines.linewidth': 1.5,
    'patch.linewidth': 0.8,
    'grid.linewidth': 0.5,
    'xtick.major.width': 0.8,
    'ytick.major.width': 0.8,
    'xtick.minor.width': 0.5,
    'ytick.minor.width': 0.5,
    'axes.labelweight': 'normal',
    'axes.titleweight': 'normal',
    'figure.titleweight': 'normal',
    'mathtext.fontset': 'stix',
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.grid': True,
    'grid.alpha': 0.3
})

# 学术配色方案 - 专业且克制
colors = {
    'primary': '#1f77b4',      # 蓝色
    'secondary': '#ff7f0e',    # 橙色
    'tertiary': '#2ca02c',     # 绿色
    'quaternary': '#d62728',   # 红色
    'text': '#000000',         # 黑色
    'grid': '#cccccc',         # 浅灰
    'background': '#ffffff'    # 白色
}

# 创建主图形 - 调整布局避免遮挡
fig = plt.figure(figsize=(30, 25))
fig.patch.set_facecolor(colors['background'])

# ==================== 子图 (a): 高维崎岖曲面 ====================
ax1 = plt.subplot2grid((2, 3), (0, 0), projection='3d')

# 创建极其崎岖不平的高维曲面
np.random.seed(42)
x = np.linspace(-3, 3, 60)
y = np.linspace(-3, 3, 60)
X, Y = np.meshgrid(x, y)

# 多层次的崎岖函数 - 模拟高维复杂性
Z = (0.5 * (X**2 + Y**2) + 
     2 * np.sin(2*X) * np.cos(2*Y) + 
     1.5 * np.sin(4*X + 1) * np.cos(3*Y - 0.5) +
     0.8 * np.sin(6*X - 2) * np.cos(5*Y + 1) +
     0.3 * np.random.randn(*X.shape))  # 随机噪声

# 绘制崎岖曲面
surf = ax1.plot_surface(X, Y, Z, cmap='terrain', alpha=0.8, 
                       linewidth=0, antialiased=True, shade=True)

# 添加等高线投影
contours = ax1.contour(X, Y, Z, levels=15, colors='gray', 
                      alpha=0.5, linewidths=0.8, zdir='z', offset=Z.min()-1)

# 标记几个关键点（局部最小值）
critical_points = [(-1.5, -1.5), (0.8, 1.2), (1.8, -0.8)]
critical_z = []
for i, (x_pt, y_pt) in enumerate(critical_points):
    z_pt = (0.5 * (x_pt**2 + y_pt**2) + 
            2 * np.sin(2*x_pt) * np.cos(2*y_pt) + 
            1.5 * np.sin(4*x_pt + 1) * np.cos(3*y_pt - 0.5) +
            0.8 * np.sin(6*x_pt - 2) * np.cos(5*y_pt + 1))
    critical_z.append(z_pt)
    
    color = [colors['primary'], colors['secondary'], colors['tertiary']][i]
    # 在曲面上的原始点
    ax1.scatter([x_pt], [y_pt], [z_pt], s=150, c=color, alpha=1.0, 
               edgecolors='white', linewidth=3)
    
    # 在提升路径上的点
    z_elevated = z_pt + 3
    ax1.scatter([x_pt], [y_pt], [z_elevated], s=150, c=color, alpha=1.0, 
               edgecolors='white', linewidth=3)
    
    # 连接线（支撑柱）
    ax1.plot([x_pt, x_pt], [y_pt, y_pt], [z_pt, z_elevated], 
             color=color, linewidth=4, alpha=0.7, linestyle='--')
    
    ax1.text(x_pt, y_pt, z_elevated+1.5, f'$p_{i}$', fontsize=12, 
             ha='center', va='bottom', weight='bold')

# 添加最小能量路径连接这些点
path_x = [pt[0] for pt in critical_points]
path_y = [pt[1] for pt in critical_points]
path_z = critical_z

# 绘制连接路径 - 提升高度确保可见
# 将路径提升到曲面上方
path_z_elevated = [z + 3 for z in path_z]  # 提升3个单位
ax1.plot(path_x, path_y, path_z_elevated, color='red', linewidth=8, alpha=1.0, 
         label='Minimum energy path', linestyle='-')

# 添加路径上的箭头指示方向 - 同样提升高度
for i in range(len(critical_points)-1):
    mid_x = (path_x[i] + path_x[i+1]) / 2
    mid_y = (path_y[i] + path_y[i+1]) / 2
    mid_z = (path_z_elevated[i] + path_z_elevated[i+1]) / 2  # 使用提升后的高度
    dx = path_x[i+1] - path_x[i]
    dy = path_y[i+1] - path_y[i]
    dz = 0  # 保持箭头水平
    
    ax1.quiver(mid_x, mid_y, mid_z, dx*0.3, dy*0.3, dz*0.3, 
               color='yellow', alpha=1.0, arrow_length_ratio=0.5, linewidth=3)

ax1.set_xlabel('High-dim space $x_1$')
ax1.set_ylabel('High-dim space $x_2$') 
ax1.set_zlabel('Energy landscape')
ax1.set_title('(a) Rugged High-Dimensional Surface')
ax1.legend(fontsize=10)
ax1.view_init(elev=25, azim=45)

# ==================== 子图 (b): 高维流形嵌入 ====================
ax2 = plt.subplot2grid((2, 3), (0, 1))

# 创建更复杂的高维流形投影
theta = np.linspace(0, 4*np.pi, 400)
phi = np.linspace(0, 2*np.pi, 400)
T, P = np.meshgrid(theta[:200], phi[:200])

# 高维流形的2D投影 - 更多噪声
x_manifold = np.cos(T) * (3 + np.cos(P)) + 0.8*np.random.randn(*T.shape)
y_manifold = np.sin(T) * (3 + np.cos(P)) + 0.8*np.random.randn(*T.shape)

# 绘制流形点云
ax2.scatter(x_manifold.flatten(), y_manifold.flatten(), 
           c=T.flatten(), cmap='viridis', alpha=0.4, s=1)

# 添加轨迹线
trajectory_theta = np.linspace(0, 4*np.pi, 100)
traj_x = np.cos(trajectory_theta) * 3.5
traj_y = np.sin(trajectory_theta) * 3.5

ax2.plot(traj_x, traj_y, color=colors['quaternary'], linewidth=4, 
         alpha=1.0, label='Optimal trajectory')

# 添加噪声轨迹对比 - 使用对比鲜明的颜色
noisy_traj_x = traj_x + 0.5*np.random.randn(len(traj_x))
noisy_traj_y = traj_y + 0.5*np.random.randn(len(traj_y))
ax2.plot(noisy_traj_x, noisy_traj_y, '--', color='cyan', 
         linewidth=4, alpha=1.0, label='Noisy trajectory')

ax2.set_xlabel('Manifold coordinate 1')
ax2.set_ylabel('Manifold coordinate 2')
ax2.set_title('(b) High-Dimensional Manifold Embedding')
ax2.legend(fontsize=8)
ax2.set_xlim(-6, 6)
ax2.set_ylim(-6, 6)

# ==================== 子图 (c): 1D密度演化 ====================
ax3 = plt.subplot2grid((2, 3), (0, 2))

# 时间和分布
t_obs = [0, 0.3, 0.7, 1.0]
x_range = np.linspace(-4, 4, 200)
means = [-2.5, -0.5, 1.0, 2.5]
stds = [0.6, 0.4, 0.5, 0.7]

# 创建更丰富的密度演化
colors_dens = [colors['primary'], colors['secondary'], colors['tertiary'], colors['quaternary']]

for i, (t, mean, std, color) in enumerate(zip(t_obs, means, stds, colors_dens)):
    # 混合高斯分布
    gaussian1 = np.exp(-0.5*((x_range - mean)/std)**2) / (std*np.sqrt(2*np.pi))
    gaussian2 = 0.3*np.exp(-0.5*((x_range - mean + 1)/std)**2) / (std*np.sqrt(2*np.pi))
    gaussian = gaussian1 + gaussian2
    
    # 添加噪声
    noise = 0.05*np.random.randn(len(x_range))
    gaussian += noise
    gaussian = np.maximum(gaussian, 0)  # 确保非负
    
    ax3.fill_between(x_range, gaussian*0.25 + t, t, alpha=0.6, color=color)
    ax3.plot(x_range, gaussian*0.25 + t, color=color, linewidth=1.5)
    ax3.text(mean, t + 0.3, f'$\\rho_{i}(t)$', fontsize=9, ha='center', va='bottom')

# 平滑轨迹
t_smooth = np.linspace(0, 1, 100)
mean_smooth = -2.5 + 5*t_smooth
ax3.plot(mean_smooth, t_smooth, color='black', linewidth=2.5, 
         alpha=0.8, label='Mean evolution')

ax3.set_xlabel('State space')
ax3.set_ylabel('Time')
ax3.set_title('(c) Density Evolution', pad=20)  # 增加标题间距
ax3.legend(fontsize=8)
ax3.set_xlim(-4, 4)
ax3.set_ylim(-0.05, 1.25)  # 增加上方空间

# ==================== 子图 (d): 参数相图 ====================
ax4 = plt.subplot2grid((2, 3), (1, 0))

sigma_values = np.logspace(-2, 2, 50)
w2_weight = 1 / (1 + sigma_values**2)
fr_weight = sigma_values**2 / (1 + sigma_values**2)

ax4.fill_between(sigma_values, w2_weight, alpha=0.3, color=colors['primary'], label='$W_2$ region')
ax4.fill_between(sigma_values, fr_weight, alpha=0.3, color=colors['secondary'], label='Fisher-Rao region')

ax4.semilogx(sigma_values, w2_weight, color=colors['primary'], linewidth=2.5)
ax4.semilogx(sigma_values, fr_weight, color=colors['secondary'], linewidth=2.5)

# 临界点
ax4.axvline(x=1, color=colors['quaternary'], linestyle='--', linewidth=2, alpha=0.8)
ax4.text(1, 0.6, '$\\sigma_c = 1$\nCritical point', fontsize=9, ha='center', 
         bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

ax4.set_xlabel('Diffusion parameter $\\sigma$')
ax4.set_ylabel('Relative weight')
ax4.set_title('(d) Parameter Phase Diagram')
ax4.legend(fontsize=8)
ax4.set_ylim(0, 1)

# ==================== 子图 (e): 能量地形 ====================
ax5 = plt.subplot2grid((2, 3), (1, 1))

x = np.linspace(-3, 3, 100)
y = np.linspace(-3, 3, 100)
X, Y = np.meshgrid(x, y)

# 复杂能量地形
Z = 0.1*(X**4 - 2*X**2 + Y**2) + 0.05*np.sin(3*X)*np.cos(3*Y)

# 美丽的等高线
levels = np.linspace(Z.min(), Z.max(), 20)
contour_filled = ax5.contourf(X, Y, Z, levels=levels, alpha=0.7, cmap='RdYlBu_r')
contour_lines = ax5.contour(X, Y, Z, levels=levels[::2], colors='black', alpha=0.4, linewidths=0.8)

# 标记鞍点和最小值
critical_points = [(-1.414, 0), (1.414, 0), (0, 0)]
point_types = ['Min', 'Min', 'Saddle']
for (x_pt, y_pt), pt_type in zip(critical_points, point_types):
    color = colors['tertiary'] if pt_type == 'Min' else colors['quaternary']
    ax5.plot(x_pt, y_pt, 'o', color=color, markersize=8, markeredgecolor='white', markeredgewidth=2)
    ax5.text(x_pt, y_pt-0.4, pt_type, fontsize=8, ha='center', weight='bold')

ax5.set_xlabel('Configuration space $x_1$')
ax5.set_ylabel('Configuration space $x_2$')
ax5.set_title('(e) Energy Landscape')
ax5.set_xlim(-3, 3)
ax5.set_ylim(-3, 3)

# ==================== 子图 (f): 收敛性分析 - 改进版 ====================
ax6 = plt.subplot2grid((2, 3), (1, 2))

iterations = np.arange(1, 101)

# 设置随机种子确保可重现性
np.random.seed(123)

# Wasserstein only - 较慢收敛
error_w2 = 10 * np.exp(-0.08*iterations) + 0.3*np.random.exponential(0.1, 100)

# Fisher-Rao only - 中等收敛
error_fr = 8 * np.exp(-0.06*iterations) + 0.2*np.random.exponential(0.1, 100)

# Mixed metric - 更自然的收敛曲线
error_mixed_base = 6 * np.exp(-0.12*iterations)
# 添加一些自然的波动，但整体趋势下降
oscillation = 0.3 * np.sin(0.3*iterations) * np.exp(-0.05*iterations)
small_noise = 0.15 * np.random.exponential(0.1, 100) * np.exp(-0.08*iterations)
error_mixed = error_mixed_base + oscillation + small_noise

# 确保整体趋势是下降的，但允许小幅波动
for i in range(5, len(error_mixed)):
    if error_mixed[i] > error_mixed[i-5] * 1.2:  # 如果上升太多，就限制
        error_mixed[i] = error_mixed[i-5] * (0.8 + 0.2*np.random.random())

ax6.semilogy(iterations, error_w2, color=colors['primary'], 
             linewidth=2, alpha=0.8, label='Wasserstein only')
ax6.semilogy(iterations, error_fr, color=colors['secondary'], 
             linewidth=2, alpha=0.8, label='Fisher-Rao only')
ax6.semilogy(iterations, error_mixed, color=colors['tertiary'], 
             linewidth=3, alpha=1.0, label='Mixed metric (optimal)')

# 添加理论界限
theoretical_bound = 2 * np.exp(-0.2*iterations)
ax6.semilogy(iterations, theoretical_bound, '--', color='gray', 
             linewidth=1.5, alpha=0.7, label='Theoretical bound')

ax6.set_xlabel('Iteration')
ax6.set_ylabel('Convergence error')
ax6.set_title('(f) Convergence Analysis')
ax6.legend(fontsize=8)
ax6.set_ylim(1e-3, 20)

# 调整布局 - 增加更多空间避免遮挡
plt.tight_layout()
plt.subplots_adjust(left=0.04, right=0.98, top=0.96, bottom=0.04, hspace=0.5, wspace=0.2)

# 保存为高质量PDF
with PdfPages('variational_smoothing_stunning.pdf') as pdf:
    pdf.savefig(fig, bbox_inches='tight', dpi=300)

plt.show()

print("🌟 Stunning high-dimensional visualization created!")
print("✨ Features enhanced:")
print("   - (a) Extremely rugged high-dimensional surface")
print("   - (b) Complex high-dimensional manifold embedding")  
print("   - (c) Rich density evolution with proper layout")
print("   - (d) Beautiful parameter phase diagram")
print("   - (e) Complex energy landscape topology")
print("   - (f) Natural convergence curves with realistic fluctuations")
print("📄 PDF saved as 'variational_smoothing_stunning.pdf'")
print("🚀 Eye-catching, publication-ready visualization!")