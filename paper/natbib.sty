%%
%% This is file `natbib.sty',
%% generated with the docstrip utility.
%%
%% The original source files were:
%%
%% natbib.dtx  (with options: `package,all')
%% =============================================
%% IMPORTANT NOTICE:
%% 
%% This program can be redistributed and/or modified under the terms
%% of the LaTeX Project Public License Distributed from CTAN
%% archives in directory macros/latex/base/lppl.txt; either
%% version 1 of the License, or any later version.
%% 
%% This is a generated file.
%% It may not be distributed without the original source file natbib.dtx.
%% 
%% Full documentation can be obtained by LaTeXing that original file.
%% Only a few abbreviated comments remain here to describe the usage.
%% =============================================
%% Copyright 1993-2009 Patrick W Daly
%% Max-Planck-Institut f\"ur Sonnensystemforschung
%% Max-Planck-Str. 2
%% D-37191 Katlenburg-Lindau
%% Germany
%% E-mail: <EMAIL>
\NeedsTeXFormat{LaTeX2e}[1995/06/01]
\ProvidesPackage{natbib}
        [2009/07/16 8.31 (PWD, AO)]

 % This package reimplements the LaTeX \cite command to be used for various
 % citation styles, both author-year and numerical. It accepts BibTeX
 % output intended for many other packages, and therefore acts as a
 % general, all-purpose citation-style interface.
 %
 % With standard numerical .bst files, only numerical citations are
 % possible. With an author-year .bst file, both numerical and
 % author-year citations are possible.
 %
 % If author-year citations are selected, \bibitem must have one of the
 %   following forms:
 %   \bibitem[Jones et al.(1990)]{key}...
 %   \bibitem[Jones et al.(1990)Jones, Baker, and Williams]{key}...
 %   \bibitem[Jones et al., 1990]{key}...
 %   \bibitem[\protect\citeauthoryear{Jones, Baker, and Williams}{Jones
 %       et al.}{1990}]{key}...
 %   \bibitem[\protect\citeauthoryear{Jones et al.}{1990}]{key}...
 %   \bibitem[\protect\astroncite{Jones et al.}{1990}]{key}...
 %   \bibitem[\protect\citename{Jones et al., }1990]{key}...
 %   \harvarditem[Jones et al.]{Jones, Baker, and Williams}{1990}{key}...
 %
 % This is either to be made up manually, or to be generated by an
 % appropriate .bst file with BibTeX.
 %                            Author-year mode     ||   Numerical mode
 % Then, \citet{key}  ==>>  Jones et al. (1990)    ||   Jones et al. [21]
 %       \citep{key}  ==>> (Jones et al., 1990)    ||   [21]
 % Multiple citations as normal:
 % \citep{key1,key2}  ==>> (Jones et al., 1990; Smith, 1989) || [21,24]
 %                           or  (Jones et al., 1990, 1991)  || [21,24]
 %                           or  (Jones et al., 1990a,b)     || [21,24]
 % \cite{key} is the equivalent of \citet{key} in author-year mode
 %                         and  of \citep{key} in numerical mode
 % Full author lists may be forced with \citet* or \citep*, e.g.
 %       \citep*{key}      ==>> (Jones, Baker, and Williams, 1990)
 % Optional notes as:
 %   \citep[chap. 2]{key}    ==>> (Jones et al., 1990, chap. 2)
 %   \citep[e.g.,][]{key}    ==>> (e.g., Jones et al., 1990)
 %   \citep[see][pg. 34]{key}==>> (see Jones et al., 1990, pg. 34)
 %  (Note: in standard LaTeX, only one note is allowed, after the ref.
 %   Here, one note is like the standard, two make pre- and post-notes.)
 %   \citealt{key}          ==>> Jones et al. 1990
 %   \citealt*{key}         ==>> Jones, Baker, and Williams 1990
 %   \citealp{key}          ==>> Jones et al., 1990
 %   \citealp*{key}         ==>> Jones, Baker, and Williams, 1990
 % Additional citation possibilities (both author-year and numerical modes)
 %   \citeauthor{key}       ==>> Jones et al.
 %   \citeauthor*{key}      ==>> Jones, Baker, and Williams
 %   \citeyear{key}         ==>> 1990
 %   \citeyearpar{key}      ==>> (1990)
 %   \citetext{priv. comm.} ==>> (priv. comm.)
 %   \citenum{key}          ==>> 11 [non-superscripted]
 % Note: full author lists depends on whether the bib style supports them;
 %       if not, the abbreviated list is printed even when full requested.
 %
 % For names like della Robbia at the start of a sentence, use
 %   \Citet{dRob98}         ==>> Della Robbia (1998)
 %   \Citep{dRob98}         ==>> (Della Robbia, 1998)
 %   \Citeauthor{dRob98}    ==>> Della Robbia
 %
 %
 % Citation aliasing is achieved with
 %   \defcitealias{key}{text}
 %   \citetalias{key}  ==>> text
 %   \citepalias{key}  ==>> (text)
 %
 % Defining the citation mode and punctual (citation style)
 %   \setcitestyle{<comma-separated list of keywords, same
 %     as the package options>}
 % Example: \setcitestyle{square,semicolon}
 % Alternatively:
 % Use \bibpunct with 6 mandatory arguments:
 %    1. opening bracket for citation
 %    2. closing bracket
 %    3. citation separator (for multiple citations in one \cite)
 %    4. the letter n for numerical styles, s for superscripts
 %        else anything for author-year
 %    5. punctuation between authors and date
 %    6. punctuation between years (or numbers) when common authors missing
 % One optional argument is the character coming before post-notes. It
 %   appears in square braces before all other arguments. May be left off.
 % Example (and default) \bibpunct[, ]{(}{)}{;}{a}{,}{,}
 %
 % To make this automatic for a given bib style, named newbib, say, make
 % a local configuration file, natbib.cfg, with the definition
 %   \newcommand{\bibstyle@newbib}{\bibpunct...}
 % Then the \bibliographystyle{newbib} will cause \bibstyle@newbib to
 % be called on THE NEXT LATEX RUN (via the aux file).
 %
 % Such preprogrammed definitions may be invoked anywhere in the text
 %  by calling \citestyle{newbib}. This is only useful if the style specified
 %  differs from that in \bibliographystyle.
 %
 % With \citeindextrue and \citeindexfalse, one can control whether the
 % \cite commands make an automatic entry of the citation in the .idx
 % indexing file. For this, \makeindex must also be given in the preamble.
 %
 % Package Options: (for selecting punctuation)
 %   round  -  round parentheses are used (default)
 %   square -  square brackets are used   [option]
 %   curly  -  curly braces are used      {option}
 %   angle  -  angle brackets are used    <option>
 %   semicolon  -  multiple citations separated by semi-colon (default)
 %   colon  - same as semicolon, an earlier confusion
 %   comma  -  separated by comma
 %   authoryear - selects author-year citations (default)
 %   numbers-  selects numerical citations
 %   super  -  numerical citations as superscripts
 %   sort   -  sorts multiple citations according to order in ref. list
 %   sort&compress   -  like sort, but also compresses numerical citations
 %   compress - compresses without sorting
 %   longnamesfirst  -  makes first citation full author list
 %   sectionbib - puts bibliography in a \section* instead of \chapter*
 %   merge - allows the citation key to have a * prefix,
 %           signifying to merge its reference with that of the previous citation.
 %   elide - if references are merged, repeated portions of later ones may be removed.
 %   mcite - recognizes and ignores the * prefix for merging.
 % Punctuation so selected dominates over any predefined ones.
 % Package options are called as, e.g.
 %        \usepackage[square,comma]{natbib}
 % LaTeX the source file natbib.dtx to obtain more details
 % or the file natnotes.tex for a brief reference sheet.
 %-----------------------------------------------------------
\providecommand\@ifxundefined[1]{%
 \ifx#1\@undefined\expandafter\@firstoftwo\else\expandafter\@secondoftwo\fi
}%
\providecommand\@ifnum[1]{%
 \ifnum#1\expandafter\@firstoftwo\else\expandafter\@secondoftwo\fi
}%
\providecommand\@ifx[1]{%
 \ifx#1\expandafter\@firstoftwo\else\expandafter\@secondoftwo\fi
}%
\providecommand\appdef[2]{%
 \toks@\expandafter{#1}\@temptokena{#2}%
 \edef#1{\the\toks@\the\@temptokena}%
}%
\@ifclassloaded{agu2001}{\PackageError{natbib}
  {The agu2001 class already includes natbib coding,\MessageBreak
   so you should not add it explicitly}
  {Type <Return> for now, but then later remove\MessageBreak
   the command \protect\usepackage{natbib} from the document}
  \endinput}{}
\@ifclassloaded{agutex}{\PackageError{natbib}
  {The AGUTeX class already includes natbib coding,\MessageBreak
   so you should not add it explicitly}
  {Type <Return> for now, but then later remove\MessageBreak
   the command \protect\usepackage{natbib} from the document}
  \endinput}{}
\@ifclassloaded{aguplus}{\PackageError{natbib}
  {The aguplus class already includes natbib coding,\MessageBreak
   so you should not add it explicitly}
  {Type <Return> for now, but then later remove\MessageBreak
   the command \protect\usepackage{natbib} from the document}
  \endinput}{}
\@ifclassloaded{nlinproc}{\PackageError{natbib}
  {The nlinproc class already includes natbib coding,\MessageBreak
   so you should not add it explicitly}
  {Type <Return> for now, but then later remove\MessageBreak
   the command \protect\usepackage{natbib} from the document}
  \endinput}{}
\@ifclassloaded{egs}{\PackageError{natbib}
  {The egs class already includes natbib coding,\MessageBreak
   so you should not add it explicitly}
  {Type <Return> for now, but then later remove\MessageBreak
   the command \protect\usepackage{natbib} from the document}
  \endinput}{}
\@ifclassloaded{egu}{\PackageError{natbib}
  {The egu class already includes natbib coding,\MessageBreak
   so you should not add it explicitly}
  {Type <Return> for now, but then later remove\MessageBreak
   the command \protect\usepackage{natbib} from the document}
  \endinput}{}
 % Define citation punctuation for some author-year styles
 % One may add and delete at this point
 % Or put additions into local configuration file natbib.cfg
\newcommand\bibstyle@chicago{\bibpunct{(}{)}{;}{a}{,}{,}}
\newcommand\bibstyle@named{\bibpunct{[}{]}{;}{a}{,}{,}}
\newcommand\bibstyle@agu{\bibpunct{[}{]}{;}{a}{,}{,~}}%Amer. Geophys. Union
\newcommand\bibstyle@copernicus{\bibpunct{(}{)}{;}{a}{,}{,}}%Copernicus Publications
\let\bibstyle@egu=\bibstyle@copernicus
\let\bibstyle@egs=\bibstyle@copernicus
\newcommand\bibstyle@agsm{\bibpunct{(}{)}{,}{a}{}{,}\gdef\harvardand{\&}}
\newcommand\bibstyle@kluwer{\bibpunct{(}{)}{,}{a}{}{,}\gdef\harvardand{\&}}
\newcommand\bibstyle@dcu{\bibpunct{(}{)}{;}{a}{;}{,}\gdef\harvardand{and}}
\newcommand\bibstyle@aa{\bibpunct{(}{)}{;}{a}{}{,}} %Astronomy & Astrophysics
\newcommand\bibstyle@pass{\bibpunct{(}{)}{;}{a}{,}{,}}%Planet. & Space Sci
\newcommand\bibstyle@anngeo{\bibpunct{(}{)}{;}{a}{,}{,}}%Annales Geophysicae
\newcommand\bibstyle@nlinproc{\bibpunct{(}{)}{;}{a}{,}{,}}%Nonlin.Proc.Geophys.
 % Define citation punctuation for some numerical styles
\newcommand\bibstyle@cospar{\bibpunct{/}{/}{,}{n}{}{}%
     \gdef\bibnumfmt##1{##1.}}
\newcommand\bibstyle@esa{\bibpunct{(Ref.~}{)}{,}{n}{}{}%
     \gdef\bibnumfmt##1{##1.\hspace{1em}}}
\newcommand\bibstyle@nature{\bibpunct{}{}{,}{s}{}{\textsuperscript{,}}%
     \gdef\bibnumfmt##1{##1.}}
 % The standard LaTeX styles
\newcommand\bibstyle@plain{\bibpunct{[}{]}{,}{n}{}{,}}
\let\bibstyle@alpha=\bibstyle@plain
\let\bibstyle@abbrv=\bibstyle@plain
\let\bibstyle@unsrt=\bibstyle@plain
 % The author-year modifications of the standard styles
\newcommand\bibstyle@plainnat{\bibpunct{[}{]}{,}{a}{,}{,}}
\let\bibstyle@abbrvnat=\bibstyle@plainnat
\let\bibstyle@unsrtnat=\bibstyle@plainnat
\newif\ifNAT@numbers \NAT@numbersfalse
\newif\ifNAT@super \NAT@superfalse
\let\NAT@merge\z@
\DeclareOption{numbers}{\NAT@numberstrue
   \ExecuteOptions{square,comma,nobibstyle}}
\DeclareOption{super}{\NAT@supertrue\NAT@numberstrue
   \renewcommand\NAT@open{}\renewcommand\NAT@close{}
   \ExecuteOptions{nobibstyle}}
\DeclareOption{authoryear}{\NAT@numbersfalse
   \ExecuteOptions{round,semicolon,bibstyle}}
\DeclareOption{round}{%
      \renewcommand\NAT@open{(} \renewcommand\NAT@close{)}
   \ExecuteOptions{nobibstyle}}
\DeclareOption{square}{%
      \renewcommand\NAT@open{[} \renewcommand\NAT@close{]}
   \ExecuteOptions{nobibstyle}}
\DeclareOption{angle}{%
      \renewcommand\NAT@open{$<$} \renewcommand\NAT@close{$>$}
   \ExecuteOptions{nobibstyle}}
\DeclareOption{curly}{%
      \renewcommand\NAT@open{\{} \renewcommand\NAT@close{\}}
   \ExecuteOptions{nobibstyle}}
\DeclareOption{comma}{\renewcommand\NAT@sep{,}
   \ExecuteOptions{nobibstyle}}
\DeclareOption{semicolon}{\renewcommand\NAT@sep{;}
   \ExecuteOptions{nobibstyle}}
\DeclareOption{colon}{\ExecuteOptions{semicolon}}
\DeclareOption{nobibstyle}{\let\bibstyle=\@gobble}
\DeclareOption{bibstyle}{\let\bibstyle=\@citestyle}
\newif\ifNAT@openbib \NAT@openbibfalse
\DeclareOption{openbib}{\NAT@openbibtrue}
\DeclareOption{sectionbib}{\def\NAT@sectionbib{on}}
\def\NAT@sort{\z@}
\def\NAT@cmprs{\z@}
\DeclareOption{sort}{\def\NAT@sort{\@ne}}
\DeclareOption{compress}{\def\NAT@cmprs{\@ne}}
\DeclareOption{sort&compress}{\def\NAT@sort{\@ne}\def\NAT@cmprs{\@ne}}
\DeclareOption{mcite}{\let\NAT@merge\@ne}
\DeclareOption{merge}{\@ifnum{\NAT@merge<\tw@}{\let\NAT@merge\tw@}{}}
\DeclareOption{elide}{\@ifnum{\NAT@merge<\thr@@}{\let\NAT@merge\thr@@}{}}
\@ifpackageloaded{cite}{\PackageWarningNoLine{natbib}
  {The `cite' package should not be used\MessageBreak
   with natbib. Use option `sort' instead}\ExecuteOptions{sort}}{}
\@ifpackageloaded{mcite}{\PackageWarningNoLine{natbib}
  {The `mcite' package should not be used\MessageBreak
   with natbib. Use option `merge' instead}\ExecuteOptions{merge}}{}
\@ifpackageloaded{citeref}{\PackageError{natbib}
  {The `citeref' package must be loaded after natbib}%
  {Move \protect\usepackage{citeref} to after \string\usepackage{natbib}}}{}
\newif\ifNAT@longnames\NAT@longnamesfalse
\DeclareOption{longnamesfirst}{\NAT@longnamestrue}
\DeclareOption{nonamebreak}{\def\NAT@nmfmt#1{\mbox{\NAT@up#1}}}
\def\NAT@nmfmt#1{{\NAT@up#1}}
\renewcommand\bibstyle[1]{\csname bibstyle@#1\endcsname}
\AtBeginDocument{\global\let\bibstyle=\@gobble}
\let\@citestyle\bibstyle
\newcommand\citestyle[1]{\@citestyle{#1}\let\bibstyle\@gobble}
\newcommand\bibpunct[7][, ]%
  {\gdef\NAT@open{#2}\gdef\NAT@close{#3}\gdef
   \NAT@sep{#4}\global\NAT@numbersfalse
     \ifx #5n\global\NAT@numberstrue\global\NAT@superfalse
   \else
     \ifx #5s\global\NAT@numberstrue\global\NAT@supertrue
   \fi\fi
   \gdef\NAT@aysep{#6}\gdef\NAT@yrsep{#7}%
   \gdef\NAT@cmt{#1}%
   \NAT@@setcites
  }
\newcommand\setcitestyle[1]{
 \@for\@tempa:=#1\do
 {\def\@tempb{round}\ifx\@tempa\@tempb
    \renewcommand\NAT@open{(}\renewcommand\NAT@close{)}\fi
  \def\@tempb{square}\ifx\@tempa\@tempb
    \renewcommand\NAT@open{[}\renewcommand\NAT@close{]}\fi
  \def\@tempb{angle}\ifx\@tempa\@tempb
    \renewcommand\NAT@open{$<$}\renewcommand\NAT@close{$>$}\fi
  \def\@tempb{curly}\ifx\@tempa\@tempb
    \renewcommand\NAT@open{\{}\renewcommand\NAT@close{\}}\fi
  \def\@tempb{semicolon}\ifx\@tempa\@tempb
    \renewcommand\NAT@sep{;}\fi
  \def\@tempb{colon}\ifx\@tempa\@tempb
    \renewcommand\NAT@sep{;}\fi
  \def\@tempb{comma}\ifx\@tempa\@tempb
    \renewcommand\NAT@sep{,}\fi
  \def\@tempb{authoryear}\ifx\@tempa\@tempb
    \NAT@numbersfalse\fi
  \def\@tempb{numbers}\ifx\@tempa\@tempb
    \NAT@numberstrue\NAT@superfalse\fi
  \def\@tempb{super}\ifx\@tempa\@tempb
    \NAT@numberstrue\NAT@supertrue\fi
  \expandafter\NAT@find@eq\@tempa=\relax\@nil
  \if\@tempc\relax\else
    \expandafter\NAT@rem@eq\@tempc
    \def\@tempb{open}\ifx\@tempa\@tempb
     \xdef\NAT@open{\@tempc}\fi
    \def\@tempb{close}\ifx\@tempa\@tempb
     \xdef\NAT@close{\@tempc}\fi
    \def\@tempb{aysep}\ifx\@tempa\@tempb
     \xdef\NAT@aysep{\@tempc}\fi
    \def\@tempb{yysep}\ifx\@tempa\@tempb
     \xdef\NAT@yrsep{\@tempc}\fi
    \def\@tempb{notesep}\ifx\@tempa\@tempb
     \xdef\NAT@cmt{\@tempc}\fi
    \def\@tempb{citesep}\ifx\@tempa\@tempb
     \xdef\NAT@sep{\@tempc}\fi
  \fi
 }%
 \NAT@@setcites
}
 \def\NAT@find@eq#1=#2\@nil{\def\@tempa{#1}\def\@tempc{#2}}
 \def\NAT@rem@eq#1={\def\@tempc{#1}}
 \def\NAT@@setcites{\global\let\bibstyle\@gobble}
\AtBeginDocument{\let\NAT@@setcites\NAT@set@cites}
\newcommand\NAT@open{(} \newcommand\NAT@close{)}
\newcommand\NAT@sep{;}
\ProcessOptions
\newcommand\NAT@aysep{,} \newcommand\NAT@yrsep{,}
\newcommand\NAT@cmt{, }
\newcommand\NAT@cite%
    [3]{\ifNAT@swa\NAT@@open\if*#2*\else#2\NAT@spacechar\fi
        #1\if*#3*\else\NAT@cmt#3\fi\NAT@@close\else#1\fi\endgroup}
\newcommand\NAT@citenum%
    [3]{\ifNAT@swa\NAT@@open\if*#2*\else#2\NAT@spacechar\fi
        #1\if*#3*\else\NAT@cmt#3\fi\NAT@@close\else#1\fi\endgroup}
\newcommand\NAT@citesuper[3]{\ifNAT@swa
\if*#2*\else#2\NAT@spacechar\fi
\unskip\kern\p@\textsuperscript{\NAT@@open#1\NAT@@close}%
   \if*#3*\else\NAT@spacechar#3\fi\else #1\fi\endgroup}
\providecommand\textsuperscript[1]{\mbox{$^{\mbox{\scriptsize#1}}$}}
\begingroup \catcode`\_=8
\gdef\NAT@ifcat@num#1{%
 \ifcat_\ifnum\z@<0#1_\else A\fi
  \expandafter\@firstoftwo
 \else
  \expandafter\@secondoftwo
 \fi
}%
\endgroup
\providecommand\@firstofone[1]{#1}
\newcommand\NAT@citexnum{}
\def\NAT@citexnum[#1][#2]#3{%
  \NAT@reset@parser
  \NAT@sort@cites{#3}%
  \NAT@reset@citea
  \@cite{\def\NAT@num{-1}\let\NAT@last@yr\relax\let\NAT@nm\@empty
    \@for\@citeb:=\NAT@cite@list\do
    {\@safe@activestrue
     \edef\@citeb{\expandafter\@firstofone\@citeb\@empty}%
     \@safe@activesfalse
     \@ifundefined{b@\@citeb\@extra@b@citeb}{%
       {\reset@font\bfseries?}
        \NAT@citeundefined\PackageWarning{natbib}%
       {Citation `\@citeb' on page \thepage \space undefined}}%
     {\let\NAT@last@num\NAT@num\let\NAT@last@nm\NAT@nm
      \NAT@parse{\@citeb}%
      \ifNAT@longnames\@ifundefined{bv@\@citeb\@extra@b@citeb}{%
        \let\NAT@name=\NAT@all@names
        \global\@namedef{bv@\@citeb\@extra@b@citeb}{}}{}%
      \fi
      \ifNAT@full\let\NAT@nm\NAT@all@names\else
        \let\NAT@nm\NAT@name\fi
      \ifNAT@swa
       \@ifnum{\NAT@ctype>\@ne}{%
        \@citea
        \NAT@hyper@{\@ifnum{\NAT@ctype=\tw@}{\NAT@test{\NAT@ctype}}{\NAT@alias}}%
       }{%
        \@ifnum{\NAT@cmprs>\z@}{%
         \NAT@ifcat@num\NAT@num
          {\let\NAT@nm=\NAT@num}%
          {\def\NAT@nm{-2}}%
         \NAT@ifcat@num\NAT@last@num
          {\@tempcnta=\NAT@last@num\relax}%
          {\@tempcnta\m@ne}%
         \@ifnum{\NAT@nm=\@tempcnta}{%
          \@ifnum{\NAT@merge>\@ne}{}{\NAT@last@yr@mbox}%
         }{%
           \advance\@tempcnta by\@ne
           \@ifnum{\NAT@nm=\@tempcnta}{%
             \ifx\NAT@last@yr\relax
               \def@NAT@last@yr{\@citea}%
             \else
               \def@NAT@last@yr{--\NAT@penalty}%
             \fi
           }{%
             \NAT@last@yr@mbox
           }%
         }%
        }{%
         \@tempswatrue
         \@ifnum{\NAT@merge>\@ne}{\@ifnum{\NAT@last@num=\NAT@num\relax}{\@tempswafalse}{}}{}%
         \if@tempswa\NAT@citea@mbox\fi
        }%
       }%
       \NAT@def@citea
      \else
        \ifcase\NAT@ctype
          \ifx\NAT@last@nm\NAT@nm \NAT@yrsep\NAT@penalty\NAT@space\else
            \@citea \NAT@test{\@ne}\NAT@spacechar\NAT@mbox{\NAT@super@kern\NAT@@open}%
          \fi
          \if*#1*\else#1\NAT@spacechar\fi
          \NAT@mbox{\NAT@hyper@{{\citenumfont{\NAT@num}}}}%
          \NAT@def@citea@box
        \or
          \NAT@hyper@citea@space{\NAT@test{\NAT@ctype}}%
        \or
          \NAT@hyper@citea@space{\NAT@test{\NAT@ctype}}%
        \or
          \NAT@hyper@citea@space\NAT@alias
        \fi
      \fi
     }%
    }%
      \@ifnum{\NAT@cmprs>\z@}{\NAT@last@yr}{}%
      \ifNAT@swa\else
        \@ifnum{\NAT@ctype=\z@}{%
          \if*#2*\else\NAT@cmt#2\fi
        }{}%
        \NAT@mbox{\NAT@@close}%
      \fi
  }{#1}{#2}%
}%
\def\NAT@citea@mbox{%
 \@citea\mbox{\NAT@hyper@{{\citenumfont{\NAT@num}}}}%
}%
\def\NAT@hyper@#1{%
 \hyper@natlinkstart{\@citeb\@extra@b@citeb}#1\hyper@natlinkend
}%
\def\NAT@hyper@citea#1{%
 \@citea
 \NAT@hyper@{#1}%
 \NAT@def@citea
}%
\def\NAT@hyper@citea@space#1{%
 \@citea
 \NAT@hyper@{#1}%
 \NAT@def@citea@space
}%
\def\def@NAT@last@yr#1{%
 \protected@edef\NAT@last@yr{%
  #1%
  \noexpand\mbox{%
   \noexpand\hyper@natlinkstart{\@citeb\@extra@b@citeb}%
   {\noexpand\citenumfont{\NAT@num}}%
   \noexpand\hyper@natlinkend
  }%
 }%
}%
\def\NAT@last@yr@mbox{%
 \NAT@last@yr\let\NAT@last@yr\relax
 \NAT@citea@mbox
}%
\newcommand\NAT@test[1]{%
 \@ifnum{#1=\@ne}{%
  \ifx\NAT@nm\NAT@noname
   \begingroup\reset@font\bfseries(author?)\endgroup
   \PackageWarning{natbib}{%
    Author undefined for citation`\@citeb' \MessageBreak on page \thepage%
   }%
  \else \NAT@nm
  \fi
 }{%
  \if\relax\NAT@date\relax
   \begingroup\reset@font\bfseries(year?)\endgroup
   \PackageWarning{natbib}{%
    Year undefined for citation`\@citeb' \MessageBreak on page \thepage%
   }%
  \else \NAT@date
  \fi
 }%
}%
\let\citenumfont=\@empty
\newcommand\NAT@citex{}
\def\NAT@citex%
  [#1][#2]#3{%
  \NAT@reset@parser
  \NAT@sort@cites{#3}%
  \NAT@reset@citea
  \@cite{\let\NAT@nm\@empty\let\NAT@year\@empty
    \@for\@citeb:=\NAT@cite@list\do
    {\@safe@activestrue
     \edef\@citeb{\expandafter\@firstofone\@citeb\@empty}%
     \@safe@activesfalse
     \@ifundefined{b@\@citeb\@extra@b@citeb}{\@citea%
       {\reset@font\bfseries ?}\NAT@citeundefined
                 \PackageWarning{natbib}%
       {Citation `\@citeb' on page \thepage \space undefined}\def\NAT@date{}}%
     {\let\NAT@last@nm=\NAT@nm\let\NAT@last@yr=\NAT@year
      \NAT@parse{\@citeb}%
      \ifNAT@longnames\@ifundefined{bv@\@citeb\@extra@b@citeb}{%
        \let\NAT@name=\NAT@all@names
        \global\@namedef{bv@\@citeb\@extra@b@citeb}{}}{}%
      \fi
     \ifNAT@full\let\NAT@nm\NAT@all@names\else
       \let\NAT@nm\NAT@name\fi
     \ifNAT@swa\ifcase\NAT@ctype
       \if\relax\NAT@date\relax
         \@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}\NAT@date}%
       \else
         \ifx\NAT@last@nm\NAT@nm\NAT@yrsep
            \ifx\NAT@last@yr\NAT@year
              \def\NAT@temp{{?}}%
              \ifx\NAT@temp\NAT@exlab\PackageWarningNoLine{natbib}%
               {Multiple citation on page \thepage: same authors and
               year\MessageBreak without distinguishing extra
               letter,\MessageBreak appears as question mark}\fi
              \NAT@hyper@{\NAT@exlab}%
            \else\unskip\NAT@spacechar
              \NAT@hyper@{\NAT@date}%
            \fi
         \else
           \@citea\NAT@hyper@{%
             \NAT@nmfmt{\NAT@nm}%
             \hyper@natlinkbreak{%
               \NAT@aysep\NAT@spacechar}{\@citeb\@extra@b@citeb
             }%
             \NAT@date
           }%
         \fi
       \fi
     \or\@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}}%
     \or\@citea\NAT@hyper@{\NAT@date}%
     \or\@citea\NAT@hyper@{\NAT@alias}%
     \fi \NAT@def@citea
     \else
       \ifcase\NAT@ctype
        \if\relax\NAT@date\relax
          \@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}}%
        \else
         \ifx\NAT@last@nm\NAT@nm\NAT@yrsep
            \ifx\NAT@last@yr\NAT@year
              \def\NAT@temp{{?}}%
              \ifx\NAT@temp\NAT@exlab\PackageWarningNoLine{natbib}%
               {Multiple citation on page \thepage: same authors and
               year\MessageBreak without distinguishing extra
               letter,\MessageBreak appears as question mark}\fi
              \NAT@hyper@{\NAT@exlab}%
            \else
              \unskip\NAT@spacechar
              \NAT@hyper@{\NAT@date}%
            \fi
         \else
           \@citea\NAT@hyper@{%
             \NAT@nmfmt{\NAT@nm}%
             \hyper@natlinkbreak{\NAT@spacechar\NAT@@open\if*#1*\else#1\NAT@spacechar\fi}%
               {\@citeb\@extra@b@citeb}%
             \NAT@date
           }%
         \fi
        \fi
       \or\@citea\NAT@hyper@{\NAT@nmfmt{\NAT@nm}}%
       \or\@citea\NAT@hyper@{\NAT@date}%
       \or\@citea\NAT@hyper@{\NAT@alias}%
       \fi
       \if\relax\NAT@date\relax
         \NAT@def@citea
       \else
         \NAT@def@citea@close
       \fi
     \fi
     }}\ifNAT@swa\else\if*#2*\else\NAT@cmt#2\fi
     \if\relax\NAT@date\relax\else\NAT@@close\fi\fi}{#1}{#2}}
\def\NAT@spacechar{\ }%
\def\NAT@separator{\NAT@sep\NAT@penalty}%
\def\NAT@reset@citea{\c@NAT@ctr\@ne\let\@citea\@empty}%
\def\NAT@def@citea{\def\@citea{\NAT@separator\NAT@space}}%
\def\NAT@def@citea@space{\def\@citea{\NAT@separator\NAT@spacechar}}%
\def\NAT@def@citea@close{\def\@citea{\NAT@@close\NAT@separator\NAT@space}}%
\def\NAT@def@citea@box{\def\@citea{\NAT@mbox{\NAT@@close}\NAT@separator\NAT@spacechar}}%
\newif\ifNAT@par \NAT@partrue
\newcommand\NAT@@open{\ifNAT@par\NAT@open\fi}
\newcommand\NAT@@close{\ifNAT@par\NAT@close\fi}
\newcommand\NAT@alias{\@ifundefined{al@\@citeb\@extra@b@citeb}{%
  {\reset@font\bfseries(alias?)}\PackageWarning{natbib}
  {Alias undefined for citation `\@citeb'
  \MessageBreak on page \thepage}}{\@nameuse{al@\@citeb\@extra@b@citeb}}}
\let\NAT@up\relax
\newcommand\NAT@Up[1]{{\let\protect\@unexpandable@protect\let~\relax
  \expandafter\NAT@deftemp#1}\expandafter\NAT@UP\NAT@temp}
\newcommand\NAT@deftemp[1]{\xdef\NAT@temp{#1}}
\newcommand\NAT@UP[1]{\let\@tempa\NAT@UP\ifcat a#1\MakeUppercase{#1}%
  \let\@tempa\relax\else#1\fi\@tempa}
\newcommand\shortcites[1]{%
  \@bsphack\@for\@citeb:=#1\do
  {\@safe@activestrue
   \edef\@citeb{\expandafter\@firstofone\@citeb\@empty}%
   \@safe@activesfalse
   \global\@namedef{bv@\@citeb\@extra@b@citeb}{}}\@esphack}
\newcommand\NAT@biblabel[1]{\hfill}
\newcommand\NAT@biblabelnum[1]{\bibnumfmt{#1}}
\let\bibnumfmt\@empty
\providecommand\@biblabel[1]{[#1]}
\AtBeginDocument{\ifx\bibnumfmt\@empty\let\bibnumfmt\@biblabel\fi}
\newcommand\NAT@bibsetnum[1]{\settowidth\labelwidth{\@biblabel{#1}}%
   \setlength{\leftmargin}{\labelwidth}\addtolength{\leftmargin}{\labelsep}%
   \setlength{\itemsep}{\bibsep}\setlength{\parsep}{\z@}%
   \ifNAT@openbib
     \addtolength{\leftmargin}{\bibindent}%
     \setlength{\itemindent}{-\bibindent}%
     \setlength{\listparindent}{\itemindent}%
     \setlength{\parsep}{0pt}%
   \fi
}
\newlength{\bibhang}
\setlength{\bibhang}{1em}
\newlength{\bibsep}
 {\@listi \global\bibsep\itemsep \global\advance\bibsep by\parsep}

\newcommand\NAT@bibsetup%
   [1]{\setlength{\leftmargin}{\bibhang}\setlength{\itemindent}{-\leftmargin}%
       \setlength{\itemsep}{\bibsep}\setlength{\parsep}{\z@}}
\newcommand\NAT@set@cites{%
  \ifNAT@numbers
    \ifNAT@super \let\@cite\NAT@citesuper
       \def\NAT@mbox##1{\unskip\nobreak\textsuperscript{##1}}%
       \let\citeyearpar=\citeyear
       \let\NAT@space\relax
       \def\NAT@super@kern{\kern\p@}%
    \else
       \let\NAT@mbox=\mbox
       \let\@cite\NAT@citenum
       \let\NAT@space\NAT@spacechar
       \let\NAT@super@kern\relax
    \fi
    \let\@citex\NAT@citexnum
    \let\@biblabel\NAT@biblabelnum
    \let\@bibsetup\NAT@bibsetnum
    \renewcommand\NAT@idxtxt{\NAT@name\NAT@spacechar\NAT@open\NAT@num\NAT@close}%
    \def\natexlab##1{}%
    \def\NAT@penalty{\penalty\@m}%
  \else
    \let\@cite\NAT@cite
    \let\@citex\NAT@citex
    \let\@biblabel\NAT@biblabel
    \let\@bibsetup\NAT@bibsetup
    \let\NAT@space\NAT@spacechar
    \let\NAT@penalty\@empty
    \renewcommand\NAT@idxtxt{\NAT@name\NAT@spacechar\NAT@open\NAT@date\NAT@close}%
    \def\natexlab##1{##1}%
  \fi}
\AtBeginDocument{\NAT@set@cites}
\AtBeginDocument{\ifx\SK@def\@undefined\else
\ifx\SK@cite\@empty\else
  \SK@def\@citex[#1][#2]#3{\SK@\SK@@ref{#3}\SK@@citex[#1][#2]{#3}}\fi
\ifx\SK@citeauthor\@undefined\def\HAR@checkdef{}\else
  \let\citeauthor\SK@citeauthor
  \let\citefullauthor\SK@citefullauthor
  \let\citeyear\SK@citeyear\fi
\fi}
\newif\ifNAT@full\NAT@fullfalse
\newif\ifNAT@swa
\DeclareRobustCommand\citet
   {\begingroup\NAT@swafalse\let\NAT@ctype\z@\NAT@partrue
     \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\newcommand\NAT@citetp{\@ifnextchar[{\NAT@@citetp}{\NAT@@citetp[]}}
\newcommand\NAT@@citetp{}
\def\NAT@@citetp[#1]{\@ifnextchar[{\@citex[#1]}{\@citex[][#1]}}
\DeclareRobustCommand\citep
   {\begingroup\NAT@swatrue\let\NAT@ctype\z@\NAT@partrue
         \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\cite
    {\begingroup\let\NAT@ctype\z@\NAT@partrue\NAT@swatrue
      \@ifstar{\NAT@fulltrue\NAT@cites}{\NAT@fullfalse\NAT@cites}}
\newcommand\NAT@cites{\@ifnextchar [{\NAT@@citetp}{%
     \ifNAT@numbers\else
     \NAT@swafalse
     \fi
    \NAT@@citetp[]}}
\DeclareRobustCommand\citealt
   {\begingroup\NAT@swafalse\let\NAT@ctype\z@\NAT@parfalse
         \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\citealp
   {\begingroup\NAT@swatrue\let\NAT@ctype\z@\NAT@parfalse
         \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\citenum
   {\begingroup
     \NAT@swatrue\let\NAT@ctype\z@\NAT@parfalse\let\textsuperscript\NAT@spacechar
     \NAT@citexnum[][]}
\DeclareRobustCommand\citeauthor
   {\begingroup\NAT@swafalse\let\NAT@ctype\@ne\NAT@parfalse
    \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\Citet
   {\begingroup\NAT@swafalse\let\NAT@ctype\z@\NAT@partrue
     \let\NAT@up\NAT@Up
     \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\Citep
   {\begingroup\NAT@swatrue\let\NAT@ctype\z@\NAT@partrue
     \let\NAT@up\NAT@Up
         \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\Citealt
   {\begingroup\NAT@swafalse\let\NAT@ctype\z@\NAT@parfalse
     \let\NAT@up\NAT@Up
         \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\Citealp
   {\begingroup\NAT@swatrue\let\NAT@ctype\z@\NAT@parfalse
     \let\NAT@up\NAT@Up
         \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\Citeauthor
   {\begingroup\NAT@swafalse\let\NAT@ctype\@ne\NAT@parfalse
     \let\NAT@up\NAT@Up
    \@ifstar{\NAT@fulltrue\NAT@citetp}{\NAT@fullfalse\NAT@citetp}}
\DeclareRobustCommand\citeyear
   {\begingroup\NAT@swafalse\let\NAT@ctype\tw@\NAT@parfalse\NAT@citetp}
\DeclareRobustCommand\citeyearpar
   {\begingroup\NAT@swatrue\let\NAT@ctype\tw@\NAT@partrue\NAT@citetp}
\newcommand\citetext[1]{\NAT@open#1\NAT@close}
\DeclareRobustCommand\citefullauthor
   {\citeauthor*}
\newcommand\defcitealias[2]{%
   \@ifundefined{al@#1\@extra@b@citeb}{}
   {\PackageWarning{natbib}{Overwriting existing alias for citation #1}}
   \@namedef{al@#1\@extra@b@citeb}{#2}}
\DeclareRobustCommand\citetalias{\begingroup
   \NAT@swafalse\let\NAT@ctype\thr@@\NAT@parfalse\NAT@citetp}
\DeclareRobustCommand\citepalias{\begingroup
   \NAT@swatrue\let\NAT@ctype\thr@@\NAT@partrue\NAT@citetp}
\renewcommand\nocite[1]{\@bsphack
  \@for\@citeb:=#1\do{%
    \@safe@activestrue
    \edef\@citeb{\expandafter\@firstofone\@citeb\@empty}%
    \@safe@activesfalse
    \if@filesw\immediate\write\@auxout{\string\citation{\@citeb}}\fi
    \if*\@citeb\else
    \@ifundefined{b@\@citeb\@extra@b@citeb}{%
       \NAT@citeundefined \PackageWarning{natbib}%
       {Citation `\@citeb' undefined}}{}\fi}%
  \@esphack}
\newcommand\NAT@parse[1]{%
  \begingroup
   \let\protect=\@unexpandable@protect
   \let~\relax
   \let\active@prefix=\@gobble
   \edef\NAT@temp{\csname b@#1\@extra@b@citeb\endcsname}%
   \aftergroup\NAT@split
   \expandafter
  \endgroup
  \NAT@temp{}{}{}{}{}@@%
  \expandafter\NAT@parse@date\NAT@date??????@@%
  \ifciteindex\NAT@index\fi
}%
\def\NAT@split#1#2#3#4#5@@{%
  \gdef\NAT@num{#1}\gdef\NAT@name{#3}\gdef\NAT@date{#2}%
  \gdef\NAT@all@names{#4}%
  \ifx\NAT@num\@empty\gdef\NAT@num{0}\fi
  \ifx\NAT@noname\NAT@all@names \gdef\NAT@all@names{#3}\fi
}%
\def\NAT@reset@parser{%
  \global\let\NAT@num\@empty
  \global\let\NAT@name\@empty
  \global\let\NAT@date\@empty
  \global\let\NAT@all@names\@empty
}%
\newcommand\NAT@parse@date{}
\def\NAT@parse@date#1#2#3#4#5#6@@{%
  \ifnum\the\catcode`#1=11\def\NAT@year{}\def\NAT@exlab{#1}\else
  \ifnum\the\catcode`#2=11\def\NAT@year{#1}\def\NAT@exlab{#2}\else
  \ifnum\the\catcode`#3=11\def\NAT@year{#1#2}\def\NAT@exlab{#3}\else
  \ifnum\the\catcode`#4=11\def\NAT@year{#1#2#3}\def\NAT@exlab{#4}\else
    \def\NAT@year{#1#2#3#4}\def\NAT@exlab{{#5}}\fi\fi\fi\fi}
\newcommand\NAT@index{}
\let\NAT@makeindex=\makeindex
\renewcommand\makeindex{\NAT@makeindex
  \renewcommand\NAT@index{\@bsphack\begingroup
     \def~{\string~}\@wrindex{\NAT@idxtxt}}}
\newcommand\NAT@idxtxt{\NAT@name\NAT@spacechar\NAT@open\NAT@date\NAT@close}
\@ifxundefined\@indexfile{}{\let\NAT@makeindex\relax\makeindex}
\newif\ifciteindex \citeindexfalse
\newcommand\citeindextype{default}
\newcommand\NAT@index@alt{{\let\protect=\noexpand\let~\relax
  \xdef\NAT@temp{\NAT@idxtxt}}\expandafter\NAT@exp\NAT@temp\@nil}
\newcommand\NAT@exp{}
\def\NAT@exp#1\@nil{\index[\citeindextype]{#1}}

\AtBeginDocument{%
\@ifpackageloaded{index}{\let\NAT@index=\NAT@index@alt}{}}
\newcommand\NAT@ifcmd{\futurelet\NAT@temp\NAT@ifxcmd}
\newcommand\NAT@ifxcmd{\ifx\NAT@temp\relax\else\expandafter\NAT@bare\fi}
\def\NAT@bare#1(#2)#3(@)#4\@nil#5{%
  \if @#2
    \expandafter\NAT@apalk#1, , \@nil{#5}%
  \else
  \NAT@wrout{\the\c@NAT@ctr}{#2}{#1}{#3}{#5}%
\fi
}
\newcommand\NAT@wrout[5]{%
\if@filesw
      {\let\protect\noexpand\let~\relax
       \immediate
       \write\@auxout{\string\bibcite{#5}{{#1}{#2}{{#3}}{{#4}}}}}\fi
\ignorespaces}
\def\NAT@noname{{}}
\renewcommand\bibitem{\@ifnextchar[{\@lbibitem}{\@lbibitem[]}}%
\let\NAT@bibitem@first@sw\@secondoftwo
\def\@lbibitem[#1]#2{%
  \if\relax\@extra@b@citeb\relax\else
    \@ifundefined{br@#2\@extra@b@citeb}{}{%
     \@namedef{br@#2}{\@nameuse{br@#2\@extra@b@citeb}}%
    }%
  \fi
  \@ifundefined{b@#2\@extra@b@citeb}{%
   \def\NAT@num{}%
  }{%
   \NAT@parse{#2}%
  }%
  \def\NAT@tmp{#1}%
  \expandafter\let\expandafter\bibitemOpen\csname NAT@b@open@#2\endcsname
  \expandafter\let\expandafter\bibitemShut\csname NAT@b@shut@#2\endcsname
  \@ifnum{\NAT@merge>\@ne}{%
   \NAT@bibitem@first@sw{%
    \@firstoftwo
   }{%
    \@ifundefined{NAT@b*@#2}{%
     \@firstoftwo
    }{%
     \expandafter\def\expandafter\NAT@num\expandafter{\the\c@NAT@ctr}%
     \@secondoftwo
    }%
   }%
  }{%
   \@firstoftwo
  }%
  {%
   \global\advance\c@NAT@ctr\@ne
   \@ifx{\NAT@tmp\@empty}{\@firstoftwo}{%
    \@secondoftwo
   }%
   {%
    \expandafter\def\expandafter\NAT@num\expandafter{\the\c@NAT@ctr}%
    \global\NAT@stdbsttrue
   }{}%
   \bibitem@fin
   \item[\hfil\NAT@anchor{#2}{\NAT@num}]%
   \global\let\NAT@bibitem@first@sw\@secondoftwo
   \NAT@bibitem@init
  }%
  {%
   \NAT@anchor{#2}{}%
   \NAT@bibitem@cont
   \bibitem@fin
  }%
  \@ifx{\NAT@tmp\@empty}{%
    \NAT@wrout{\the\c@NAT@ctr}{}{}{}{#2}%
  }{%
    \expandafter\NAT@ifcmd\NAT@tmp(@)(@)\@nil{#2}%
  }%
}%
\def\bibitem@fin{%
 \@ifxundefined\@bibstop{}{\csname bibitem@\@bibstop\endcsname}%
}%
\def\NAT@bibitem@init{%
 \let\@bibstop\@undefined
}%
\def\NAT@bibitem@cont{%
 \let\bibitem@Stop\bibitemStop
 \let\bibitem@NoStop\bibitemContinue
}%
\def\BibitemOpen{%
 \bibitemOpen
}%
\def\BibitemShut#1{%
 \bibitemShut
 \def\@bibstop{#1}%
 \let\bibitem@Stop\bibitemStop
 \let\bibitem@NoStop\bibitemNoStop
}%
\def\bibitemStop{}%
\def\bibitemNoStop{.\spacefactor\@mmm\space}%
\def\bibitemContinue{\spacefactor\@mmm\space}%
\mathchardef\@mmm=3000 %
\providecommand{\bibAnnote}[3]{%
  \BibitemShut{#1}%
  \def\@tempa{#3}\@ifx{\@tempa\@empty}{}{%
   \begin{quotation}\noindent
    \textsc{Key:}\ #2\\\textsc{Annotation:}\ \@tempa
   \end{quotation}%
  }%
}%
\providecommand{\bibAnnoteFile}[2]{%
  \IfFileExists{#2}{%
    \bibAnnote{#1}{#2}{\input{#2}}%
  }{%
    \bibAnnote{#1}{#2}{}%
  }%
}%
\let\bibitemOpen\relax
\let\bibitemShut\relax
\def\bibfield{\@ifnum{\NAT@merge>\tw@}{\@bibfield}{\@secondoftwo}}%
\def\@bibfield#1#2{%
 \begingroup
  \let\Doi\@gobble
  \let\bibinfo\relax
  \let\restore@protect\@empty
  \protected@edef\@tempa{#2}%
  \aftergroup\def\aftergroup\@tempa
 \expandafter\endgroup\expandafter{\@tempa}%
 \expandafter\@ifx\expandafter{\csname @bib#1\endcsname\@tempa}{%
  \expandafter\let\expandafter\@tempa\csname @bib@X#1\endcsname
 }{%
  \expandafter\let\csname @bib#1\endcsname\@tempa
  \expandafter\let\expandafter\@tempa\csname @bib@Y#1\endcsname
 }%
 \@ifx{\@tempa\relax}{\let\@tempa\@firstofone}{}%
 \@tempa{#2}%
}%
\def\bibinfo#1{%
 \expandafter\let\expandafter\@tempa\csname bibinfo@X@#1\endcsname
 \@ifx{\@tempa\relax}{\@firstofone}{\@tempa}%
}%
\def\@bib@Xauthor#1{\let\@bib@Xjournal\@gobble}%
\def\@bib@Xjournal#1{\begingroup\let\bibinfo@X@journal\@bib@Z@journal#1\endgroup}%
\def\@bibibid@#1{\textit{ibid}.}%
\appdef\NAT@bibitem@init{%
 \let\@bibauthor  \@empty
 \let\@bibjournal \@empty
 \let\@bib@Z@journal\@bibibid@
}%
\ifx\SK@lbibitem\@undefined\else
   \let\SK@lbibitem\@lbibitem
   \def\@lbibitem[#1]#2{%
     \SK@lbibitem[#1]{#2}\SK@\SK@@label{#2}\ignorespaces}\fi
\newif\ifNAT@stdbst \NAT@stdbstfalse

\AtEndDocument{%
  \ifNAT@stdbst\if@filesw
   \immediate\write\@auxout{%
    \string\providecommand\string\NAT@force@numbers{}%
    \string\NAT@force@numbers
   }%
  \fi\fi
 }
\newcommand\NAT@force@numbers{%
  \ifNAT@numbers\else
  \PackageError{natbib}{Bibliography not compatible with author-year
  citations.\MessageBreak
  Press <return> to continue in numerical citation style}
  {Check the bibliography entries for non-compliant syntax,\MessageBreak
   or select author-year BibTeX style, e.g. plainnat}%
  \global\NAT@numberstrue\fi}

\providecommand\bibcite{}
\renewcommand\bibcite[2]{%
 \@ifundefined{b@#1\@extra@binfo}{\relax}{%
   \NAT@citemultiple
   \PackageWarningNoLine{natbib}{Citation `#1' multiply defined}%
 }%
 \global\@namedef{b@#1\@extra@binfo}{#2}%
}%
\AtEndDocument{\NAT@swatrue\let\bibcite\NAT@testdef}
\newcommand\NAT@testdef[2]{%
  \def\NAT@temp{#2}%
  \expandafter \ifx \csname b@#1\@extra@binfo\endcsname\NAT@temp
  \else
    \ifNAT@swa \NAT@swafalse
      \PackageWarningNoLine{natbib}{%
        Citation(s) may have changed.\MessageBreak
        Rerun to get citations correct%
      }%
    \fi
  \fi
}%
\newcommand\NAT@apalk{}
\def\NAT@apalk#1, #2, #3\@nil#4{%
  \if\relax#2\relax
    \global\NAT@stdbsttrue
    \NAT@wrout{#1}{}{}{}{#4}%
  \else
    \NAT@wrout{\the\c@NAT@ctr}{#2}{#1}{}{#4}%
  \fi
}%
\newcommand\citeauthoryear{}
\def\citeauthoryear#1#2#3(@)(@)\@nil#4{%
  \if\relax#3\relax
    \NAT@wrout{\the\c@NAT@ctr}{#2}{#1}{}{#4}%
  \else
    \NAT@wrout{\the\c@NAT@ctr}{#3}{#2}{#1}{#4}%
  \fi
}%
\newcommand\citestarts{\NAT@open}%
\newcommand\citeends{\NAT@close}%
\newcommand\betweenauthors{and}%
\newcommand\astroncite{}
\def\astroncite#1#2(@)(@)\@nil#3{%
 \NAT@wrout{\the\c@NAT@ctr}{#2}{#1}{}{#3}%
}%
\newcommand\citename{}
\def\citename#1#2(@)(@)\@nil#3{\expandafter\NAT@apalk#1#2, \@nil{#3}}
\newcommand\harvarditem[4][]{%
 \if\relax#1\relax
   \bibitem[#2(#3)]{#4}%
 \else
   \bibitem[#1(#3)#2]{#4}%
 \fi
}%
\newcommand\harvardleft{\NAT@open}
\newcommand\harvardright{\NAT@close}
\newcommand\harvardyearleft{\NAT@open}
\newcommand\harvardyearright{\NAT@close}
\AtBeginDocument{\providecommand{\harvardand}{and}}
\newcommand\harvardurl[1]{\textbf{URL:} \textit{#1}}
\providecommand\bibsection{}
\@ifundefined{chapter}{%
  \renewcommand\bibsection{%
   \section*{\refname\@mkboth{\MakeUppercase{\refname}}{\MakeUppercase{\refname}}}%
  }%
}{%
  \@ifxundefined\NAT@sectionbib{%
    \renewcommand\bibsection{%
      \chapter*{\bibname\@mkboth{\MakeUppercase{\bibname}}{\MakeUppercase{\bibname}}}%
    }%
  }{%
    \renewcommand\bibsection{%
      \section*{\bibname\ifx\@mkboth\@gobbletwo\else\markright{\MakeUppercase{\bibname}}\fi}%
    }%
  }%
}%
\@ifclassloaded{amsart}{\renewcommand\bibsection{\section*{\refname}}}{}%
\@ifclassloaded{amsbook}{\renewcommand\bibsection{\chapter*{\bibname}}}{}%
\@ifxundefined\bib@heading{}{\let\bibsection\bib@heading}%
\newcounter{NAT@ctr}
\renewenvironment{thebibliography}[1]{%
 \bibsection
 \parindent\z@
 \bibpreamble
 \bibfont
 \list{\@biblabel{\the\c@NAT@ctr}}{\@bibsetup{#1}\global\c@NAT@ctr\z@}%
 \ifNAT@openbib
   \renewcommand\newblock{\par}%
 \else
   \renewcommand\newblock{\hskip .11em \@plus.33em \@minus.07em}%
 \fi
 \sloppy\clubpenalty4000\widowpenalty4000
 \sfcode`\.\@m
 \let\NAT@bibitem@first@sw\@firstoftwo
    \let\citeN\cite \let\shortcite\cite
    \let\citeasnoun\cite
}{%
 \bibitem@fin
 \bibpostamble
 \def\@noitemerr{%
  \PackageWarning{natbib}{Empty `thebibliography' environment}%
 }%
 \endlist
 \bibcleanup
}%
\let\bibfont\@empty
\let\bibpreamble\@empty
\let\bibpostamble\@empty
\def\bibcleanup{\vskip-\lastskip}%
\providecommand\reset@font{\relax}
\providecommand\bibname{Bibliography}
\providecommand\refname{References}
\newcommand\NAT@citeundefined{\gdef \NAT@undefined {%
    \PackageWarningNoLine{natbib}{There were undefined citations}}}
\let \NAT@undefined \relax
\newcommand\NAT@citemultiple{\gdef \NAT@multiple {%
    \PackageWarningNoLine{natbib}{There were multiply defined citations}}}
\let \NAT@multiple \relax
\AtEndDocument{\NAT@undefined\NAT@multiple}
\providecommand\@mkboth[2]{}
\providecommand\MakeUppercase{\uppercase}
\providecommand{\@extra@b@citeb}{}
\gdef\@extra@binfo{}
\def\NAT@anchor#1#2{%
 \hyper@natanchorstart{#1\@extra@b@citeb}%
  \def\@tempa{#2}\@ifx{\@tempa\@empty}{}{\@biblabel{#2}}%
 \hyper@natanchorend
}%
\providecommand\hyper@natanchorstart[1]{}%
\providecommand\hyper@natanchorend{}%
\providecommand\hyper@natlinkstart[1]{}%
\providecommand\hyper@natlinkend{}%
\providecommand\hyper@natlinkbreak[2]{#1}%
\AtBeginDocument{%
  \@ifpackageloaded{babel}{%
     \let\org@@citex\@citex}{}}
\providecommand\@safe@activestrue{}%
\providecommand\@safe@activesfalse{}%

\newcommand\NAT@sort@cites[1]{%
  \let\NAT@cite@list\@empty
  \@for\@citeb:=#1\do{\expandafter\NAT@star@cite\@citeb\@@}%
  \if@filesw
    \expandafter\immediate\expandafter\write\expandafter\@auxout
      \expandafter{\expandafter\string\expandafter\citation\expandafter{\NAT@cite@list}}%
  \fi
  \@ifnum{\NAT@sort>\z@}{%
    \expandafter\NAT@sort@cites@\expandafter{\NAT@cite@list}%
  }{}%
}%
\def\NAT@star@cite{%
  \let\NAT@star@sw\@secondoftwo
  \@ifnum{\NAT@merge>\z@}{%
   \@ifnextchar*{%
    \let\NAT@star@sw\@firstoftwo
    \NAT@star@cite@star
   }{%
    \NAT@star@cite@nostar
   }%
  }{%
   \NAT@star@cite@noextension
  }%
}%
\def\NAT@star@cite@star*{%
 \NAT@star@cite@nostar
}%
\def\NAT@star@cite@nostar{%
 \let\nat@keyopt@open\@empty
 \let\nat@keyopt@shut\@empty
 \@ifnextchar[{\NAT@star@cite@pre}{\NAT@star@cite@pre[]}%
}%
\def\NAT@star@cite@pre[#1]{%
 \def\nat@keyopt@open{#1}%
 \@ifnextchar[{\NAT@star@cite@post}{\NAT@star@cite@post[]}%
}%
\def\NAT@star@cite@post[#1]#2\@@{%
 \def\nat@keyopt@shut{#1}%
 \NAT@star@sw{\expandafter\global\expandafter\let\csname NAT@b*@#2\endcsname\@empty}{}%
 \NAT@cite@list@append{#2}%
}%
\def\NAT@star@cite@noextension#1\@@{%
  \let\nat@keyopt@open\@empty
  \let\nat@keyopt@shut\@empty
  \NAT@cite@list@append{#1}%
}%
\def\NAT@cite@list@append#1{%
  \edef\@citeb{\@firstofone#1\@empty}%
  \if@filesw\@ifxundefined\@cprwrite{}{\expandafter\@cprwrite\@citeb=}\fi
  \if\relax\nat@keyopt@open\relax\else
   \global\expandafter\let\csname NAT@b@open@\@citeb\endcsname\nat@keyopt@open
  \fi
  \if\relax\nat@keyopt@shut\relax\else
   \global\expandafter\let\csname NAT@b@shut@\@citeb\endcsname\nat@keyopt@shut
  \fi
  \toks@\expandafter{\NAT@cite@list}%
  \ifx\NAT@cite@list\@empty
    \@temptokena\expandafter{\@citeb}%
  \else
    \@temptokena\expandafter{\expandafter,\@citeb}%
  \fi
  \edef\NAT@cite@list{\the\toks@\the\@temptokena}%
}%
\newcommand\NAT@sort@cites@[1]{%
  \count@\z@
  \@tempcntb\m@ne
  \let\@celt\delimiter
  \def\NAT@num@list{}%
  \let\NAT@cite@list\@empty
  \let\NAT@nonsort@list\@empty
  \@for \@citeb:=#1\do{\NAT@make@cite@list}%
  \ifx\NAT@nonsort@list\@empty\else
   \protected@edef\NAT@cite@list{\NAT@cite@list\NAT@nonsort@list}%
  \fi
  \ifx\NAT@cite@list\@empty\else
   \protected@edef\NAT@cite@list{\expandafter\NAT@xcom\NAT@cite@list @@}%
  \fi
}%
\def\NAT@make@cite@list{%
  \advance\count@\@ne
  \@safe@activestrue
  \edef\@citeb{\expandafter\@firstofone\@citeb\@empty}%
  \@safe@activesfalse
  \@ifundefined{b@\@citeb\@extra@b@citeb}%
   {\def\NAT@num{A}}%
   {\NAT@parse{\@citeb}}%
  \NAT@ifcat@num\NAT@num
   {\@tempcnta\NAT@num \relax
    \@ifnum{\@tempcnta<\@tempcntb}{%
      \let\NAT@@cite@list=\NAT@cite@list
      \let\NAT@cite@list\@empty
      \begingroup\let\@celt=\NAT@celt\NAT@num@list\endgroup
      \protected@edef\NAT@num@list{%
       \expandafter\NAT@num@celt \NAT@num@list \@gobble @%
      }%
    }{%
      \protected@edef\NAT@num@list{\NAT@num@list \@celt{\NAT@num}}%
      \protected@edef\NAT@cite@list{\NAT@cite@list\@citeb,}%
      \@tempcntb\@tempcnta
    }%
   }%
   {\protected@edef\NAT@nonsort@list{\NAT@nonsort@list\@citeb,}}%
}%
\def\NAT@celt#1{%
  \@ifnum{#1>\@tempcnta}{%
    \xdef\NAT@cite@list{\NAT@cite@list\@citeb,\NAT@@cite@list}%
    \let\@celt\@gobble
  }{%
    \expandafter\def@NAT@cite@lists\NAT@@cite@list\@@
  }%
}%
\def\NAT@num@celt#1#2{%
 \ifx#1\@celt
  \@ifnum{#2>\@tempcnta}{%
    \@celt{\number\@tempcnta}%
    \@celt{#2}%
  }{%
    \@celt{#2}%
    \expandafter\NAT@num@celt
  }%
 \fi
}%
\def\def@NAT@cite@lists#1,#2\@@{%
  \xdef\NAT@cite@list{\NAT@cite@list#1,}%
  \xdef\NAT@@cite@list{#2}%
}%
\def\NAT@nextc#1,#2@@{#1,}
\def\NAT@restc#1,#2{#2}
\def\NAT@xcom#1,@@{#1}
\InputIfFileExists{natbib.cfg}
       {\typeout{Local config file natbib.cfg used}}{}
%% 
%% <<<<< End of generated file <<<<<<
%%
%% End of file `natbib.sty'.
