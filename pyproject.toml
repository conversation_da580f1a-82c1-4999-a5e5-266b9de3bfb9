[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mmsbvi"
version = "0.1.0"
description = "Multi-Marginal Schrödinger Bridge Variational Inference"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "MMSBVI Team", email = "<EMAIL>"}
]
requires-python = ">=3.11"
dependencies = [
    "jax>=0.6.2",
    "jaxlib>=0.6.2", 
    "ott-jax>=0.4.5",
    "optax>=0.1.9",
    "blackjax>=1.2.5",
    "chex>=0.1.85",
    "jaxtyping>=0.2.25",
    "numpy>=1.26.3",
    "scipy>=1.12.0",
    "matplotlib>=3.8.2",
    "seaborn>=0.13.0",
    "einops>=0.7.0",
    "hydra-core>=1.3.2",
    "rich>=13.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-cov>=4.0.0",
    "mypy>=1.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "flake8>=6.0.0",
    "pre-commit>=3.0.0",
]
viz = [
    "plotly>=5.17.0",
    "jupyter>=1.0.0",
    "ipywidgets>=8.0.0",
]
experiment = [
    "wandb>=0.16.1",
    "sacred>=0.8.4",
]

[tool.setuptools]
packages = ["mmsbvi"]
package-dir = {"" = "src"}

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
ignore_missing_imports = true
disallow_untyped_defs = true
check_untyped_defs = true
strict_optional = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
# addopts = "-v --cov=mmsbvi --cov-report=html --cov-report=term"