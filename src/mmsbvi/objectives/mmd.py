"""
流形上的最大均值差异 (Maximum Mean Discrepancy on Manifolds)

本模块为在特定流形上计算MMD损失提供了可重用的组件。它将替换
`control_grad.py` 中原有的基于重要性采样的边界惩罚项，以提供一个
更稳健、更符合几何直觉的分布匹配度量。

核心职责:
1.  核函数定义: 提供在流形上定义的核函数，例如适用于S¹的Von Mises核和
    适用于ℝ的RBF核。
2.  张量积核: 将不同分量的核函数组合成一个适用于整个乘积流形
    (Product Manifold) 的核。
3.  MMD² 估计器: 实现一个数值稳定的、无偏的MMD平方估计器。该估计器
    应能处理两个样本集合，并返回它们之间差异的标量值。
4.  可配置的计算加速: 支持通过随机子采样来减少计算复杂度，这对于大规模
    批量训练至关重要。

理论基础:
MMD²(P,Q) = ||μ_P - μ_Q||²_H，其中μ_P, μ_Q是分布P,Q在RKHS H中的嵌入。
无偏估计器: MMD²(P,Q) = E[k(X,X')] + E[k(Y,Y')] - 2E[k(X,Y)]
其中X,X'~P, Y,Y'~Q，且X≠X', Y≠Y'。

Von Mises核: k_VM(θ₁,θ₂) = exp(κ cos(θ₁-θ₂)) = exp(κ(cos θ₁ cos θ₂ + sin θ₁ sin θ₂))
RBF核: k_RBF(ω₁,ω₂) = exp(-||ω₁-ω₂||²/(2σ²))
张量积核: k(x₁,x₂) = k_VM(θ₁,θ₂) × k_RBF(ω₁,ω₂)
"""

import jax
import jax.numpy as jnp
import jax.random
from jax import pmap, vmap
from jax.experimental import shard_map
from jax.sharding import PartitionSpec as P, Mesh
from typing import Any, Dict, Optional, Tuple
from jaxtyping import Float, Array
import chex

# Type aliases for clarity
EmbeddedState = Float[Array, "... 3"]  # (cos θ, sin θ, ω)
MMDValue = Float[Array, ""]  # Scalar MMD² value
DiagnosticsDict = Dict[str, Float[Array, ""]]


@chex.dataclass  
class ManifoldMMDLoss:
    """
    流形上的最大均值差异损失函数。
    Maximum Mean Discrepancy loss on manifolds.
    
    为圆柱流形S¹×ℝ实现张量积核的MMD²估计器，结合Von Mises核(S¹)和RBF核(ℝ)。
    Implements MMD² estimator with tensor product kernel for cylindrical manifold S¹×ℝ,
    combining Von Mises kernel (S¹) and RBF kernel (ℝ).
    
    Args:
        manifold: CylindricalManifold实例，提供几何信息
        kappa: Von Mises核的集中参数κ，控制角度相似性敏感度 
        sigma_rbf: RBF核的带宽参数σ，控制速度相似性敏感度
        subsample_size: 可选的子采样大小，用于加速计算
        use_cache: 是否启用核计算缓存优化
        numerical_epsilon: 数值稳定性参数，防止除零和溢出
        
    Mathematical Foundation:
        - Von Mises kernel: k_VM(θ₁,θ₂) = exp(κ cos(θ₁-θ₂))
        - RBF kernel: k_RBF(ω₁,ω₂) = exp(-||ω₁-ω₂||²/(2σ²))  
        - Tensor product: k(x₁,x₂) = k_VM(θ₁,θ₂) × k_RBF(ω₁,ω₂)
        - Unbiased MMD²: E[k(X,X')] + E[k(Y,Y')] - 2E[k(X,Y)]
    """
    manifold: Any  # CylindricalManifold实例
    kappa: float = 1.0  # Von Mises核参数κ
    sigma_rbf: float = 1.0  # RBF核带宽参数σ
    subsample_size: Optional[int] = None  # 子采样大小
    use_cache: bool = True  # 核缓存优化开关
    numerical_epsilon: float = 1e-8  # 数值稳定性参数
    
    def __post_init__(self):
        """
        **GPU OPTIMIZED**: 后初始化验证和GPU并行化设置
        Post-initialization validation and GPU parallelization setup
        
        注意：由于JAX JIT编译会将dataclass字段转换为追踪器，
        这里的验证只在非JIT环境下执行。
        """
        # 只在非追踪状态下进行验证
        try:
            # 检查是否在JAX追踪中（如果是，会抛出异常）
            float(self.kappa) 
            float(self.sigma_rbf)
            float(self.numerical_epsilon)
            
            # 验证核心参数为正数
            assert isinstance(self.kappa, (int, float)) and self.kappa > 0, \
                f"Von Mises核参数kappa必须为正数，得到: {self.kappa}"
            assert isinstance(self.sigma_rbf, (int, float)) and self.sigma_rbf > 0, \
                f"RBF核带宽参数sigma_rbf必须为正数，得到: {self.sigma_rbf}"
            assert isinstance(self.numerical_epsilon, (int, float)) and self.numerical_epsilon > 0, \
                f"数值稳定性参数numerical_epsilon必须为正数，得到: {self.numerical_epsilon}"
            
            # 验证子采样大小合理性
            if self.subsample_size is not None:
                assert isinstance(self.subsample_size, int), "子采样大小必须为整数"
                assert self.subsample_size > 0, "子采样大小必须为正整数"
                assert self.subsample_size >= 2, "子采样大小至少为2以计算无偏MMD²"
            
            # **GPU PARALLELIZATION**: 设置GPU并行化环境
            # Setup GPU parallelization environment
            self._setup_gpu_parallelization()
                
        except (TypeError, jax.errors.TracerIntegerConversionError):
            # 在JAX追踪状态下跳过验证
            pass
    
    def _setup_gpu_parallelization(self):
        """设置GPU并行化环境 / Setup GPU parallelization environment"""
        try:
            devices = jax.devices()
            self.device_count = len(devices)
            
            if self.device_count > 1:
                # 多GPU环境：创建设备网格
                self.gpu_mesh = Mesh(devices, ("batch",))
            else:
                # 单GPU环境
                self.gpu_mesh = None
                
        except Exception:
            # 设置失败时的回退
            self.device_count = 1
            self.gpu_mesh = None
    
    @jax.jit
    def _von_mises_kernel(
        self, 
        x1: EmbeddedState, 
        x2: EmbeddedState
    ) -> Float[Array, "..."]:
        """
        Von Mises核函数，作用于S¹分量。
        Von Mises kernel for S¹ component.
        
        利用嵌入坐标的内积特性计算cos(θ₁-θ₂) = cos θ₁ cos θ₂ + sin θ₁ sin θ₂。
        Uses inner product property of embedded coordinates: cos(θ₁-θ₂) = x₁ᵀx₂.
        
        Args:
            x1, x2: 嵌入状态 [..., 3]，前两维为S¹分量[cos θ, sin θ]
            
        Returns:
            Von Mises核值 exp(κ cos(θ₁-θ₂))
            
        Note:
            数值稳定性: 裁剪内积到[-1,1]防止exp溢出
            Numerical stability: clip inner product to [-1,1] to prevent exp overflow
        """
        # 提取S¹分量: [cos θ, sin θ]
        s1_component_1 = x1[..., :2]
        s1_component_2 = x2[..., :2]
        
        # 计算内积 = cos(θ₁ - θ₂)，利用cos和sin的正交性
        cos_angle_diff = jnp.sum(s1_component_1 * s1_component_2, axis=-1)
        
        # 数值稳定性: 基于指数溢出阈值的精确裁剪
        # exp(700) ≈ 1e304接近Float64最大值，所以κ*cos_angle_diff应该<700
        max_exp_arg = 700.0 / (self.kappa + self.numerical_epsilon)
        
        # 首先确保cos_angle_diff在有效范围内
        cos_angle_diff = jnp.clip(cos_angle_diff, -1.0, 1.0)
        
        # 然后基于指数安全限制进行裁剪
        cos_angle_diff = jnp.clip(cos_angle_diff, -max_exp_arg, max_exp_arg)
        
        # Von Mises核: exp(κ cos(θ₁-θ₂))
        return jnp.exp(self.kappa * cos_angle_diff)
    
    @jax.jit
    def _rbf_kernel(
        self, 
        x1: EmbeddedState, 
        x2: EmbeddedState
    ) -> Float[Array, "..."]:
        """
        RBF(径向基函数)核，作用于ℝ分量(角速度ω)。
        RBF (Radial Basis Function) kernel for ℝ component (angular velocity ω).
        
        Args:
            x1, x2: 嵌入状态 [..., 3]，第三维为ℝ分量ω
            
        Returns:
            RBF核值 exp(-||ω₁-ω₂||²/(2σ²))
            
        Note:
            使用平方欧氏距离，适合一维速度空间
            Uses squared Euclidean distance, suitable for 1D velocity space
        """
        # 提取ℝ分量: ω (角速度)
        omega1 = x1[..., 2]
        omega2 = x2[..., 2]
        
        # 计算平方距离
        sq_dist = (omega1 - omega2) ** 2
        
        # RBF核: exp(-d²/(2σ²))
        return jnp.exp(-sq_dist / (2 * self.sigma_rbf ** 2 + self.numerical_epsilon))
    
    @jax.jit
    def _tensor_product_kernel(
        self, 
        x1: EmbeddedState, 
        x2: EmbeddedState
    ) -> Float[Array, "..."]:
        """
        张量积核，结合Von Mises核和RBF核。
        Tensor product kernel combining Von Mises and RBF kernels.
        
        k(x₁,x₂) = k_VM(θ₁,θ₂) × k_RBF(ω₁,ω₂)
        
        Args:
            x1, x2: 嵌入状态 [..., 3] = [cos θ, sin θ, ω]
            
        Returns:
            张量积核值，范围(0, exp(κ)]
            
        Note:
            张量积保证了核函数的正定性，适合MMD计算
            Tensor product preserves positive definiteness, suitable for MMD
        """
        von_mises_k = self._von_mises_kernel(x1, x2)
        rbf_k = self._rbf_kernel(x1, x2)
        return von_mises_k * rbf_k
    
    def _gpu_optimized_kernel_matrix(self, chunk_i, chunk_j):
        """
        **GPU OPTIMIZED**: GPU优化的核矩阵计算
        GPU optimized kernel matrix computation using advanced parallelization
        
        Args:
            chunk_i: 第一个数据块 [size_i, 3]
            chunk_j: 第二个数据块 [size_j, 3]
            
        Returns:
            核矩阵 [size_i, size_j]
        """
        size_i, size_j = chunk_i.shape[0], chunk_j.shape[0]
        
        if hasattr(self, 'gpu_mesh') and self.gpu_mesh is not None and self.device_count > 1:
            # **MULTI-GPU**: 使用shard_map进行多GPU并行化
            # Multi-GPU: Use shard_map for multi-GPU parallelization
            
            # 为了使用shard_map，我们需要将数据适当分片
            # 展开为一维计算，然后重构
            def kernel_computation(args):
                x1, x2 = args[0], args[1]
                return self._tensor_product_kernel(x1, x2)
            
            # 创建所有配对的展开形式 
            i_indices = jnp.arange(size_i)[:, None].repeat(size_j, axis=1).flatten()  # [size_i*size_j]
            j_indices = jnp.arange(size_j)[None, :].repeat(size_i, axis=0).flatten()  # [size_i*size_j]
            
            # 获取对应的数据点对
            chunk_i_expanded = chunk_i[i_indices]  # [size_i*size_j, 3]
            chunk_j_expanded = chunk_j[j_indices]  # [size_i*size_j, 3]
            
            # 并行计算所有核值
            paired_data = jnp.stack([chunk_i_expanded, chunk_j_expanded], axis=1)  # [size_i*size_j, 2, 3]
            
            try:
                # 尝试使用shard_map进行并行化
                sharded_fn = shard_map(
                    kernel_computation,
                    mesh=self.gpu_mesh,
                    in_specs=P("batch", None, None),
                    out_specs=P("batch")
                )
                kernel_values = sharded_fn(paired_data)
            except:
                # 回退到vmap
                kernel_values = vmap(kernel_computation)(paired_data)
            
            # 重构为矩阵形状
            return kernel_values.reshape(size_i, size_j)
        else:
            # **SINGLE-GPU**: 使用优化的嵌套vmap
            # Single-GPU: Use optimized nested vmap
            return jax.vmap(
                jax.vmap(self._tensor_product_kernel, in_axes=(None, 0)),
                in_axes=(0, None)
            )(chunk_i, chunk_j)
    
    @jax.jit
    def compute_mmd_squared(
        self,
        p_samples: Float[Array, "n 3"],
        q_samples: Float[Array, "m 3"], 
        key: jax.random.PRNGKey
    ) -> Tuple[MMDValue, DiagnosticsDict]:
        """
        计算无偏MMD²估计器。
        Compute unbiased MMD² estimator.
        
        使用U-统计量形式的无偏估计器:
        MMD²(P,Q) = 1/(n(n-1))∑ᵢ≠ⱼ k(xᵢ,xⱼ) + 1/(m(m-1))∑ᵢ≠ⱼ k(yᵢ,yⱼ) - 2/(nm)∑ᵢⱼ k(xᵢ,yⱼ)
        
        Args:
            p_samples: 第一个分布的样本 [n, 3]
            q_samples: 第二个分布的样本 [m, 3]  
            key: JAX随机数生成器密钥
            
        Returns:
            mmd_squared: MMD²值
            diagnostics: 诊断信息包含各项的贡献
            
        Note:
            - 使用vmap实现高效的批量核计算
            - 移除对角线元素以保证无偏性
            - 提供详细诊断信息用于调试
        """
        n, m = p_samples.shape[0], q_samples.shape[0]
        
        # 安全性检查：使用JAX兼容的条件处理
        insufficient_samples = jnp.logical_or(n < 2, m < 2)
        
        def _compute_normal():
            # 正常计算路径
            return self._compute_mmd_core(p_samples, q_samples, n, m)
        
        def _compute_fallback():
            # 样本不足的回退路径
            zero_mmd = jnp.array(0.0)
            diagnostics = {
                "pp_term": zero_mmd,
                "qq_term": zero_mmd,
                "pq_term": zero_mmd,
                "effective_n": jnp.asarray(n, dtype=jnp.float64),
                "effective_m": jnp.asarray(m, dtype=jnp.float64),
                "kernel_pp_mean": zero_mmd,  # 保持结构一致
                "kernel_qq_mean": zero_mmd,  # 保持结构一致
                "kernel_pq_mean": zero_mmd,  # 保持结构一致
                "chunked_computation": jnp.array(0.0),  # 保持结构一致
                "warning": jnp.array(1.0)  # 表示样本数不足的警告
            }
            return zero_mmd, diagnostics
        
        return jax.lax.cond(insufficient_samples, _compute_fallback, _compute_normal)
    
    def _compute_mmd_core(self, p_samples, q_samples, n, m):
        """
        **SIMPLIFIED**: 简化版MMD²估计器，避免复杂分块逻辑
        Simplified MMD² estimator avoiding complex chunking logic
        
        对于测试和中等规模数据，使用直接计算避免JIT编译问题
        For testing and medium-scale data, use direct computation to avoid JIT issues
        """
        
        # **DIRECT COMPUTATION**: 直接计算核矩阵（适用于测试规模）
        # Direct kernel matrix computation (suitable for test scale)
        
        # **PP TERM**: E[k(X,X')] where X≠X' ~ P
        K_pp = jax.vmap(
            jax.vmap(self._tensor_product_kernel, in_axes=(None, 0)),
            in_axes=(0, None)
        )(p_samples, p_samples)
        
        # 移除对角线元素计算无偏估计
        pp_sum_off_diag = jnp.sum(K_pp) - jnp.trace(K_pp)
        term1 = pp_sum_off_diag / (n * (n - 1) + self.numerical_epsilon)
        
        # **QQ TERM**: E[k(Y,Y')] where Y≠Y' ~ Q  
        K_qq = jax.vmap(
            jax.vmap(self._tensor_product_kernel, in_axes=(None, 0)),
            in_axes=(0, None)
        )(q_samples, q_samples)
        
        qq_sum_off_diag = jnp.sum(K_qq) - jnp.trace(K_qq)
        term2 = qq_sum_off_diag / (m * (m - 1) + self.numerical_epsilon)
        
        # **PQ TERM**: -2E[k(X,Y)] where X~P, Y~Q
        K_pq = jax.vmap(
            jax.vmap(self._tensor_product_kernel, in_axes=(None, 0)),
            in_axes=(0, None)
        )(p_samples, q_samples)
        
        pq_sum = jnp.sum(K_pq)
        term3 = -2.0 * pq_sum / (n * m)
        
        # 最终MMD²估计
        mmd_squared = term1 + term2 + term3
        
        # **DIAGNOSTICS**: 提供完整的诊断信息
        diagnostics = {
            "pp_term": term1,
            "qq_term": term2, 
            "pq_term": term3,
            "effective_n": jnp.asarray(n, dtype=jnp.float64),
            "effective_m": jnp.asarray(m, dtype=jnp.float64),
            "kernel_pp_mean": jnp.mean(K_pp),
            "kernel_qq_mean": jnp.mean(K_qq),
            "kernel_pq_mean": jnp.mean(K_pq),
            "chunked_computation": jnp.array(0.0),  # 标记使用了直接计算
            "warning": jnp.array(0.0)
        }
        
        return mmd_squared, diagnostics
    
    @jax.jit
    def _compute_chunked_pp_sum(self, p_samples, n, chunk_size):
        """
        分块计算P样本之间的核和（去除对角线）
        Chunked computation of kernel sum between P samples (excluding diagonal)
        """
        # **JAX COMPATIBLE**: 预计算分块范围，避免动态切片问题
        # Pre-compute chunking ranges to avoid dynamic slicing issues
        num_chunks = jnp.ceil(n / chunk_size).astype(jnp.int32)
        
        def chunk_fn(chunk_idx_i):
            start_i = chunk_idx_i * chunk_size
            end_i = jnp.minimum(start_i + chunk_size, n)
            actual_size_i = end_i - start_i
            
            # 使用固定大小切片，然后截取有效部分
            chunk_i = jax.lax.dynamic_slice(p_samples, (start_i, 0), (chunk_size, 3))[:actual_size_i]
            
            def inner_chunk_fn(chunk_idx_j):
                start_j = chunk_idx_j * chunk_size
                end_j = jnp.minimum(start_j + chunk_size, n)
                actual_size_j = end_j - start_j
                
                chunk_j = jax.lax.dynamic_slice(p_samples, (start_j, 0), (chunk_size, 3))[:actual_size_j]
                
                # **GPU OPTIMIZED**: 计算块内核矩阵
                K_chunk = jax.vmap(
                    jax.vmap(self._tensor_product_kernel, in_axes=(None, 0)),
                    in_axes=(0, None)
                )(chunk_i, chunk_j)
                
                # 如果是对角块，需要移除对角线元素
                is_diag_block = chunk_idx_i == chunk_idx_j
                chunk_sum = jnp.where(
                    is_diag_block,
                    jnp.sum(K_chunk) - jnp.trace(K_chunk),  # 对角块：去除对角线
                    jnp.sum(K_chunk)  # 非对角块：全部求和
                )
                
                return chunk_sum
                
            # 内层分块循环
            inner_sums = jax.vmap(inner_chunk_fn)(jnp.arange(num_chunks))
            return jnp.sum(inner_sums)
        
        # 外层分块循环
        chunk_sums = jax.vmap(chunk_fn)(jnp.arange(num_chunks))
        return jnp.sum(chunk_sums)
    
    @jax.jit
    def _compute_chunked_qq_sum(self, q_samples, m, chunk_size):
        """
        分块计算Q样本之间的核和（去除对角线）
        Chunked computation of kernel sum between Q samples (excluding diagonal)
        """
        # **JAX COMPATIBLE**: 预计算分块范围
        num_chunks = jnp.ceil(m / chunk_size).astype(jnp.int32)
        
        def chunk_fn(chunk_idx_i):
            start_i = chunk_idx_i * chunk_size
            end_i = jnp.minimum(start_i + chunk_size, m)
            actual_size_i = end_i - start_i
            
            chunk_i = jax.lax.dynamic_slice(q_samples, (start_i, 0), (chunk_size, 3))[:actual_size_i]
            
            def inner_chunk_fn(chunk_idx_j):
                start_j = chunk_idx_j * chunk_size
                end_j = jnp.minimum(start_j + chunk_size, m)
                actual_size_j = end_j - start_j
                
                chunk_j = jax.lax.dynamic_slice(q_samples, (start_j, 0), (chunk_size, 3))[:actual_size_j]
                
                # **GPU OPTIMIZED**: 计算块内核矩阵
                K_chunk = jax.vmap(
                    jax.vmap(self._tensor_product_kernel, in_axes=(None, 0)),
                    in_axes=(0, None)
                )(chunk_i, chunk_j)
                
                # 如果是对角块，需要移除对角线元素
                is_diag_block = chunk_idx_i == chunk_idx_j
                chunk_sum = jnp.where(
                    is_diag_block,
                    jnp.sum(K_chunk) - jnp.trace(K_chunk),  # 对角块：去除对角线
                    jnp.sum(K_chunk)  # 非对角块：全部求和
                )
                
                return chunk_sum
                
            # 内层分块循环
            inner_sums = jax.vmap(inner_chunk_fn)(jnp.arange(num_chunks))
            return jnp.sum(inner_sums)
        
        # 外层分块循环
        chunk_sums = jax.vmap(chunk_fn)(jnp.arange(num_chunks))
        return jnp.sum(chunk_sums)
    
    @jax.jit  
    def _compute_chunked_pq_sum(self, p_samples, q_samples, n, m, chunk_size_n, chunk_size_m):
        """
        分块计算P和Q样本之间的核和
        Chunked computation of kernel sum between P and Q samples
        """
        # **JAX COMPATIBLE**: 预计算分块范围
        num_chunks_n = jnp.ceil(n / chunk_size_n).astype(jnp.int32)
        num_chunks_m = jnp.ceil(m / chunk_size_m).astype(jnp.int32)
        
        def chunk_fn(chunk_idx_i):
            start_i = chunk_idx_i * chunk_size_n
            end_i = jnp.minimum(start_i + chunk_size_n, n)
            actual_size_i = end_i - start_i
            
            chunk_p = jax.lax.dynamic_slice(p_samples, (start_i, 0), (chunk_size_n, 3))[:actual_size_i]
            
            def inner_chunk_fn(chunk_idx_j):
                start_j = chunk_idx_j * chunk_size_m
                end_j = jnp.minimum(start_j + chunk_size_m, m)
                actual_size_j = end_j - start_j
                
                chunk_q = jax.lax.dynamic_slice(q_samples, (start_j, 0), (chunk_size_m, 3))[:actual_size_j]
                
                # 计算块内核矩阵
                K_chunk = jax.vmap(
                    jax.vmap(self._tensor_product_kernel, in_axes=(None, 0)),
                    in_axes=(0, None)
                )(chunk_p, chunk_q)
                
                return jnp.sum(K_chunk)
                
            # 内层分块循环 (Q维度)
            inner_sums = jax.vmap(inner_chunk_fn)(jnp.arange(num_chunks_m))
            return jnp.sum(inner_sums)
        
        # 外层分块循环 (P维度)
        chunk_sums = jax.vmap(chunk_fn)(jnp.arange(num_chunks_n))
        return jnp.sum(chunk_sums)
    
    def compute_mmd_squared_subsampled(
        self,
        p_samples: Float[Array, "n 3"],
        q_samples: Float[Array, "m 3"],
        key: jax.random.PRNGKey
    ) -> MMDValue:
        """
        子采样MMD²计算，用于大规模数据的性能优化。
        Subsampled MMD² computation for performance optimization on large datasets.
        
        将O(n²)复杂度降低到O(k²)，其中k << n,m。
        Reduces O(n²) complexity to O(k²) where k << n,m.
        
        Args:
            p_samples: 第一个分布的样本 [n, 3]
            q_samples: 第二个分布的样本 [m, 3]
            key: JAX随机数生成器密钥
            
        Returns:
            子采样的MMD²估计值
            
        Note:
            - 如果subsample_size未设置，则使用完整数据
            - 使用无放回采样保证样本多样性
            - 适合训练阶段的快速评估
        """
        # 简化的子采样逻辑，不使用JIT以避免动态大小问题
        if self.subsample_size is None:
            return self.compute_mmd_squared(p_samples, q_samples, key)[0]
        
        n, m = p_samples.shape[0], q_samples.shape[0]
        k = min(self.subsample_size, min(n, m))
        
        if k < 2 or k >= min(n, m):
            return self.compute_mmd_squared(p_samples, q_samples, key)[0]
        
        # 使用permutation进行无放回采样
        key1, key2 = jax.random.split(key)
        p_indices = jax.random.permutation(key1, n)[:k]
        q_indices = jax.random.permutation(key2, m)[:k]
        
        p_subsample = p_samples[p_indices]
        q_subsample = q_samples[q_indices]
        
        return self.compute_mmd_squared(p_subsample, q_subsample, key)[0]
    
    def __call__(
        self,
        p_samples: Float[Array, "n 3"],
        q_samples: Float[Array, "m 3"],
        key: jax.random.PRNGKey,
        return_diagnostics: bool = False
    ) -> Tuple[MMDValue, Optional[DiagnosticsDict]]:
        """
        调用接口，根据配置选择完整或子采样计算。
        Call interface, choose full or subsampled computation based on configuration.
        
        Args:
            p_samples: 第一个分布的样本
            q_samples: 第二个分布的样本
            key: 随机数生成器密钥
            return_diagnostics: 是否返回诊断信息
            
        Returns:
            MMD²值和可选的诊断信息
        """
        if self.subsample_size is not None:
            # 子采样模式
            mmd_value = self.compute_mmd_squared_subsampled(p_samples, q_samples, key)
            diagnostics = {"subsampled": jnp.array(1.0)} if return_diagnostics else None
        else:
            # 完整计算模式
            mmd_value, full_diagnostics = self.compute_mmd_squared(p_samples, q_samples, key)
            diagnostics = full_diagnostics if return_diagnostics else None
            
        return mmd_value, diagnostics