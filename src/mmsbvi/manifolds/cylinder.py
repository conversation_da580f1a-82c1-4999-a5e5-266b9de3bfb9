"""
Cylindrical Manifold Geometry (S¹ x ℝ)
圆柱流形几何 (S¹ x ℝ)

This module provides a self-contained, JAX-native implementation for handling the
geometry of the state space for the large-angle pendulum problem.

Key Design Principles:
- Immutability and Purity: All methods are pure functions, compatible with JAX's
  functional programming paradigm and transformations like jit, vmap, etc.
- Coordinate System Abstraction: The class abstracts away the details of the
  (cos θ, sin θ, ω) embedding, providing clear conversion and projection methods.
- Mathematical Rigor: Geodesic distance and projection formulas are implemented
  with attention to numerical stability (e.g., clipping inputs to arccos).
- High Performance: All core geometric operations are JIT-compiled for maximum
  efficiency in training and validation loops.

Core Responsibilities:
1.  Coordinate Conversion: Provide mappings between the intuitive (θ, ω)
    representation and the singularity-free (cos θ, sin θ, ω) embedding used
    by the neural network.
2.  Projection: Implement a `project` method to map points from the ambient
    Euclidean space (ℝ³) back onto the cylindrical manifold. This is crucial
    for projected SDE integrators.
3.  Metric Tensor (Implicit): Define a `geodesic_distance_sq` method that
    encapsulates the anisotropic metric of the cylinder, allowing for
    physically meaningful distance calculations. The `beta` parameter controls
    the relative cost of movement in the angular vs. velocity dimensions.
"""

import jax
import jax.numpy as jnp
from typing import Tuple, Optional
from jaxtyping import Float, Array
import chex

# 导入混合精度支持
from ..core.mixed_precision import MixedPrecisionManager, mixed_precision_fn

# Type aliases for clarity
AngleVelocity = Tuple[Float[Array, "..."], Float[Array, "..."]]  # (θ, ω)
EmbeddedState = Float[Array, "... 3"]  # (cos θ, sin θ, ω) 
Distance = Float[Array, "..."]


@chex.dataclass
class CylindricalManifold:
    """
    **MIXED PRECISION OPTIMIZED**: Cylindrical manifold S¹ × ℝ for large-angle pendulum dynamics.
    **混合精度优化**：圆柱流形 S¹ × ℝ，用于大角度单摆动力学。
    
    The manifold represents the state space where:
    - θ ∈ [0, 2π) is the angle (periodic)
    - ω ∈ ℝ is the angular velocity
    - Embedding: (θ, ω) → (cos θ, sin θ, ω) ∈ ℝ³
    - Constraint: cos²θ + sin²θ = 1
    
    **PERFORMANCE OPTIMIZATIONS**:
    - Mixed precision support with bfloat16 computation, float32 storage
    - JIT compilation for all geometric operations
    - Numerically stable implementations with proper clipping
    - Hardware-aware precision management
    
    Args:
        beta: Weight parameter for velocity dimension in geodesic distance.
              Controls relative cost of angular vs velocity movement.
              值越大，角速度维度的距离权重越大。
        mixed_precision_manager: Optional mixed precision manager for GPU optimization.
              If provided, enables bfloat16 computation with float32 storage.
              如果提供，启用bfloat16计算和float32存储。
    """
    beta: float = 1.0
    mixed_precision_manager: Optional[MixedPrecisionManager] = None
    
    def __post_init__(self):
        """后初始化：设置混合精度优化 / Post-init: Setup mixed precision optimization"""
        if self.mixed_precision_manager is not None:
            # 为所有几何操作启用混合精度
            # Enable mixed precision for all geometric operations
            self._setup_mixed_precision_methods()
    
    def _setup_mixed_precision_methods(self):
        """设置混合精度方法包装器 / Setup mixed precision method wrappers"""
        if self.mixed_precision_manager is None:
            return
            
        # 包装关键几何方法
        # Wrap key geometric methods
        original_from_angle_velocity = self.from_angle_velocity
        original_to_angle_velocity = self.to_angle_velocity  
        original_project = self.project
        original_geodesic_distance_sq = self.geodesic_distance_sq
        
        # 应用混合精度装饰器
        # Apply mixed precision decorators
        self.from_angle_velocity = mixed_precision_fn(self.mixed_precision_manager)(original_from_angle_velocity)
        self.to_angle_velocity = mixed_precision_fn(self.mixed_precision_manager)(original_to_angle_velocity)
        self.project = mixed_precision_fn(self.mixed_precision_manager)(original_project)
        self.geodesic_distance_sq = mixed_precision_fn(self.mixed_precision_manager)(original_geodesic_distance_sq)
    
    @staticmethod
    @jax.jit
    def from_angle_velocity(
        theta: Float[Array, "..."], 
        omega: Float[Array, "..."]
    ) -> EmbeddedState:
        """
        Convert from (θ, ω) coordinates to embedded representation (cos θ, sin θ, ω).
        将(θ, ω)坐标转换为嵌入表示(cos θ, sin θ, ω)。
        
        Args:
            theta: Angle values θ ∈ [0, 2π)
            omega: Angular velocity values ω ∈ ℝ
            
        Returns:
            Embedded state (cos θ, sin θ, ω) ∈ ℝ³ satisfying ||(cos θ, sin θ)||₂ = 1
            
        Note:
            This transformation is numerically stable and avoids singularities
            that occur with direct angle representation.
            这种变换数值稳定，避免了直接角度表示的奇异性。
        """
        cos_theta = jnp.cos(theta)
        sin_theta = jnp.sin(theta)
        return jnp.stack([cos_theta, sin_theta, omega], axis=-1)
    
    @staticmethod
    @jax.jit  
    def to_angle_velocity(x: EmbeddedState) -> AngleVelocity:
        """
        Convert from embedded representation (cos θ, sin θ, ω) back to (θ, ω).
        将嵌入表示(cos θ, sin θ, ω)转换回(θ, ω)。
        
        Args:
            x: Embedded state (cos θ, sin θ, ω) ∈ ℝ³
            
        Returns:
            Tuple (theta, omega) where:
            - theta ∈ [0, 2π) recovered using atan2 for numerical stability
            - omega = x[..., 2] (unchanged)
            
        Note:
            Uses jnp.arctan2 for numerically stable angle recovery,
            handling all quadrants correctly including edge cases.
            使用jnp.arctan2进行数值稳定的角度恢复，正确处理所有象限包括边界情况。
        """
        cos_theta = x[..., 0]
        sin_theta = x[..., 1] 
        omega = x[..., 2]
        
        # Use atan2 for numerical stability and correct quadrant handling
        # 使用atan2保证数值稳定性和正确的象限处理
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        # Ensure theta ∈ [0, 2π) 
        theta = jnp.where(theta < 0, theta + 2 * jnp.pi, theta)
        
        return theta, omega
    
    @staticmethod
    @jax.jit
    def project(x: Float[Array, "... 3"], eps: float = 1e-8) -> EmbeddedState:
        """
        Project arbitrary points in ℝ³ onto the cylindrical manifold S¹ × ℝ.
        将ℝ³中的任意点投影到圆柱流形S¹ × ℝ上。
        
        Args:
            x: Points in ambient space ℝ³
            eps: Small epsilon for numerical stability in normalization
            
        Returns:
            Projected points on S¹ × ℝ where the first two components are normalized
            to unit length: ||(x₀, x₁)||₂ = 1, and x₂ is unchanged.
            
        Note:
            The projection normalizes the S¹ component (first two coordinates)
            while keeping the ℝ component (third coordinate) unchanged.
            This is essential for projected SDE integration schemes.
            投影将S¹分量（前两个坐标）归一化，同时保持ℝ分量（第三个坐标）不变。
            这对投影SDE积分方案至关重要。
        """
        # Extract S¹ and ℝ components
        s1_component = x[..., :2]  # (cos θ, sin θ)
        r_component = x[..., 2:3]  # ω
        
        # Compute norm of S¹ component with numerical stability
        # 计算S¹分量的范数，保证数值稳定性
        s1_norm = jnp.linalg.norm(s1_component, axis=-1, keepdims=True)
        
        # Normalize S¹ component, adding eps to prevent division by zero
        # 归一化S¹分量，加入eps防止除零
        s1_normalized = s1_component / (s1_norm + eps)
        
        # Concatenate normalized S¹ component with unchanged ℝ component  
        # 连接归一化的S¹分量和不变的ℝ分量
        return jnp.concatenate([s1_normalized, r_component], axis=-1)
    
    @jax.jit
    def geodesic_distance_sq(
        self, 
        x1: EmbeddedState, 
        x2: EmbeddedState
    ) -> Distance:
        """
        Compute squared geodesic distance on the cylindrical manifold.
        计算圆柱流形上的测地距离平方。
        
        The geodesic distance on S¹ × ℝ with anisotropic metric is:
        d²(x₁, x₂) = (θ₁ - θ₂)² + β(ω₁ - ω₂)²
        
        where the angular difference is computed as the shorter arc on S¹.
        其中角度差计算为S¹上的短弧。
        
        Args:
            x1, x2: Points on the cylindrical manifold (cos θ, sin θ, ω)
            
        Returns:
            Squared geodesic distance with anisotropic weighting
            
        Note:
            Uses numerically stable computation of angular differences via
            dot product and arccos with proper clipping for stability.
            通过点积和带适当裁剪的arccos进行角度差的数值稳定计算。
        """
        # Extract S¹ components (cos θ, sin θ)
        s1_x1 = x1[..., :2]
        s1_x2 = x2[..., :2]
        
        # Extract ℝ components (ω)  
        omega1 = x1[..., 2]
        omega2 = x2[..., 2]
        
        # Compute angular difference using dot product
        # 使用点积计算角度差
        cos_angle_diff = jnp.sum(s1_x1 * s1_x2, axis=-1)
        
        # Clip to [-1, 1] for numerical stability in arccos
        # 裁剪到[-1, 1]以保证arccos的数值稳定性
        cos_angle_diff = jnp.clip(cos_angle_diff, -1.0 + 1e-8, 1.0 - 1e-8)
        
        # Compute angular difference as the shorter arc on S¹
        # 计算S¹上的短弧角度差
        angle_diff = jnp.arccos(cos_angle_diff)
        
        # Compute velocity difference
        # 计算速度差
        omega_diff = omega1 - omega2
        
        # Return anisotropic squared geodesic distance
        # 返回各向异性的测地距离平方
        return angle_diff**2 + self.beta * omega_diff**2