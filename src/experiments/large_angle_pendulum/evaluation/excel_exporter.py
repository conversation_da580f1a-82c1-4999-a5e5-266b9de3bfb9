"""
评估指标Excel导出模块

提供完整的Excel导出功能，便于指标分析和可视化查看
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from pathlib import Path
import datetime
from dataclasses import asdict
import json

from .metrics import PendulumEvaluationMetrics


class MetricsExcelExporter:
    """评估指标Excel导出器"""
    
    def __init__(self, output_dir: str = "results/evaluation"):
        """
        初始化导出器
        
        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def export_single_experiment(self, 
                               metrics: PendulumEvaluationMetrics,
                               experiment_name: str,
                               config: Optional[Dict[str, Any]] = None) -> str:
        """
        导出单个实验的指标
        
        Args:
            metrics: 评估指标
            experiment_name: 实验名称
            config: 实验配置（可选）
            
        Returns:
            导出文件路径
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{experiment_name}_{timestamp}.xlsx"
        filepath = self.output_dir / filename
        
        # 创建Excel writer
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            
            # 1. 主要指标概览表
            main_metrics_data = {
                '指标名称': [
                    'NLL (负对数似然度)',
                    'Bimodality Score (双峰性评分)', 
                    'Coverage 95% (95%覆盖率)',
                    'MSE (均方误差)',
                    'MAE (平均绝对误差)',
                    'Circular MSE (圆形MSE)',
                    'Energy Conservation Error (能量守恒误差)',
                    'Phase Correlation (相位相关性)',
                    'Winding Number Accuracy (缠绕数准确性)',
                    'Manifold Consistency (流形一致性)',
                    'Computational Time (计算时间)',
                    'Memory Usage (内存使用量)'
                ],
                '数值': [
                    metrics.nll,
                    metrics.bimodality_score,
                    metrics.coverage_95,
                    metrics.mse,
                    metrics.mae,
                    metrics.circular_mse,
                    metrics.energy_conservation_error,
                    metrics.phase_correlation,
                    metrics.winding_number_accuracy,
                    metrics.manifold_consistency,
                    metrics.computational_time,
                    metrics.memory_usage
                ],
                '单位/说明': [
                    'nats (越小越好)',
                    '0-1范围 (越高越好表示双峰性)',
                    '0-1范围 (越接近0.95越好)',
                    '数值 (越小越好)',
                    '数值 (越小越好)',
                    '数值 (越小越好)',
                    '数值 (越小越好)',
                    '-1到1 (越接近1越好)',
                    '0-1范围 (越接近1越好)',
                    '数值 (越小越好)',
                    '秒',
                    'MB'
                ]
            }
            
            df_main = pd.DataFrame(main_metrics_data)
            df_main.to_excel(writer, sheet_name='主要指标', index=False)
            
            # 2. 性能评估表
            performance_data = {
                '性能指标': ['NLL', 'Coverage 95%', 'Bimodality Score'],
                '数值': [metrics.nll, metrics.coverage_95, metrics.bimodality_score],
                '评估等级': [
                    self._evaluate_nll(metrics.nll),
                    self._evaluate_coverage(metrics.coverage_95),
                    self._evaluate_bimodality(metrics.bimodality_score)
                ]
            }
            
            df_performance = pd.DataFrame(performance_data)
            df_performance.to_excel(writer, sheet_name='性能评估', index=False)
            
            # 3. 物理一致性表
            physics_data = {
                '物理指标': [
                    'Energy Conservation Error',
                    'Phase Correlation', 
                    'Manifold Consistency',
                    'Winding Number Accuracy'
                ],
                '数值': [
                    metrics.energy_conservation_error,
                    metrics.phase_correlation,
                    metrics.manifold_consistency,
                    metrics.winding_number_accuracy
                ],
                '物理意义': [
                    '能量守恒程度（单摆基本物理定律）',
                    '相位一致性（角度预测准确性）',
                    '流形约束满足程度（cos²θ + sin²θ = 1）',
                    '缠绕数准确性（拓扑性质保持）'
                ]
            }
            
            df_physics = pd.DataFrame(physics_data)
            df_physics.to_excel(writer, sheet_name='物理一致性', index=False)
            
            # 4. 计算资源表
            resource_data = {
                '资源指标': ['计算时间', '内存使用量'],
                '数值': [metrics.computational_time, metrics.memory_usage],
                '单位': ['秒', 'MB'],
                '效率评估': [
                    self._evaluate_time(metrics.computational_time),
                    self._evaluate_memory(metrics.memory_usage)
                ]
            }
            
            df_resource = pd.DataFrame(resource_data)
            df_resource.to_excel(writer, sheet_name='计算资源', index=False)
            
            # 5. 实验配置表（如果提供）
            if config:
                config_data = []
                for key, value in config.items():
                    config_data.append({
                        '配置项': key,
                        '值': str(value),
                        '类型': type(value).__name__
                    })
                
                df_config = pd.DataFrame(config_data)
                df_config.to_excel(writer, sheet_name='实验配置', index=False)
            
            # 6. 元数据表
            metadata = {
                '项目': ['MMSBVI Large Angle Pendulum'],
                '实验名称': [experiment_name],
                '导出时间': [datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                '总体评分': [self._compute_overall_score(metrics)]
            }
            
            df_metadata = pd.DataFrame(metadata)
            df_metadata.to_excel(writer, sheet_name='元数据', index=False)
        
        print(f"✅ 指标数据已导出至: {filepath}")
        return str(filepath)
    
    def export_multiple_experiments(self,
                                  experiments_data: List[Dict[str, Any]],
                                  comparison_name: str = "comparison") -> str:
        """
        导出多个实验的对比分析
        
        Args:
            experiments_data: 实验数据列表，每个包含 {'name': str, 'metrics': PendulumEvaluationMetrics, 'config': dict}
            comparison_name: 对比分析名称
            
        Returns:
            导出文件路径
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{comparison_name}_{timestamp}.xlsx"
        filepath = self.output_dir / filename
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            
            # 1. 所有实验主要指标对比表
            comparison_data = {
                '实验名称': [exp['name'] for exp in experiments_data]
            }
            
            metric_names = [
                ('NLL', 'nll'),
                ('Bimodality Score', 'bimodality_score'),
                ('Coverage 95%', 'coverage_95'),
                ('MSE', 'mse'),
                ('MAE', 'mae'),
                ('Circular MSE', 'circular_mse'),
                ('Energy Error', 'energy_conservation_error'),
                ('Phase Correlation', 'phase_correlation'),
                ('Winding Accuracy', 'winding_number_accuracy'),
                ('Manifold Consistency', 'manifold_consistency'),
                ('Time (s)', 'computational_time'),
                ('Memory (MB)', 'memory_usage')
            ]
            
            for display_name, attr_name in metric_names:
                comparison_data[display_name] = [
                    getattr(exp['metrics'], attr_name) for exp in experiments_data
                ]
            
            df_comparison = pd.DataFrame(comparison_data)
            df_comparison.to_excel(writer, sheet_name='实验对比', index=False)
            
            # 2. 排名分析表
            ranking_data = self._create_ranking_analysis(experiments_data)
            df_ranking = pd.DataFrame(ranking_data)
            df_ranking.to_excel(writer, sheet_name='排名分析', index=False)
            
            # 3. 最佳实践推荐表
            best_practices = self._analyze_best_practices(experiments_data)
            df_best = pd.DataFrame(best_practices)
            df_best.to_excel(writer, sheet_name='最佳实践', index=False)
        
        print(f"✅ 对比分析已导出至: {filepath}")
        return str(filepath)
    
    def _evaluate_nll(self, nll: float) -> str:
        """评估NLL等级"""
        if nll < 1.0:
            return "优秀"
        elif nll < 2.0:
            return "良好"
        elif nll < 3.0:
            return "一般"
        else:
            return "需改进"
    
    def _evaluate_coverage(self, coverage: float) -> str:
        """评估覆盖率等级"""
        if abs(coverage - 0.95) < 0.02:
            return "优秀"
        elif abs(coverage - 0.95) < 0.05:
            return "良好"
        elif abs(coverage - 0.95) < 0.10:
            return "一般"
        else:
            return "需改进"
    
    def _evaluate_bimodality(self, bimodality: float) -> str:
        """评估双峰性等级"""
        if bimodality > 0.8:
            return "强双峰性"
        elif bimodality > 0.6:
            return "中等双峰性"
        elif bimodality > 0.4:
            return "弱双峰性"
        else:
            return "单峰性"
    
    def _evaluate_time(self, time: float) -> str:
        """评估计算时间"""
        if time < 60:
            return "快速"
        elif time < 300:
            return "适中"
        elif time < 1800:
            return "较慢"
        else:
            return "缓慢"
    
    def _evaluate_memory(self, memory: float) -> str:
        """评估内存使用"""
        if memory < 1000:
            return "低消耗"
        elif memory < 4000:
            return "中等消耗"
        elif memory < 8000:
            return "高消耗"
        else:
            return "极高消耗"
    
    def _compute_overall_score(self, metrics: PendulumEvaluationMetrics) -> float:
        """计算总体评分（0-100）"""
        # 标准化各项指标并加权
        nll_score = max(0, 100 - metrics.nll * 20)  # NLL越小越好
        coverage_score = 100 - abs(metrics.coverage_95 - 0.95) * 200  # 越接近0.95越好
        bimodality_score = metrics.bimodality_score * 100  # 双峰性
        consistency_score = max(0, 100 - metrics.manifold_consistency * 100)  # 流形一致性
        
        # 加权平均
        overall_score = (
            nll_score * 0.3 +
            coverage_score * 0.3 +
            bimodality_score * 0.2 +
            consistency_score * 0.2
        )
        
        return round(overall_score, 2)
    
    def _create_ranking_analysis(self, experiments_data: List[Dict[str, Any]]) -> Dict[str, List]:
        """创建排名分析"""
        experiments = experiments_data
        n_exp = len(experiments)
        
        ranking_data = {
            '指标': [],
            '最佳实验': [],
            '最佳值': [],
            '最差实验': [],
            '最差值': []
        }
        
        metrics_to_rank = [
            ('NLL', 'nll', True),  # True表示越小越好
            ('Coverage 95%', 'coverage_95', False),  # False表示越接近0.95越好
            ('Bimodality Score', 'bimodality_score', False),  # False表示越大越好
            ('MSE', 'mse', True),
            ('Energy Error', 'energy_conservation_error', True),
            ('Phase Correlation', 'phase_correlation', False),
            ('Computational Time', 'computational_time', True)
        ]
        
        for display_name, attr_name, smaller_better in metrics_to_rank:
            values = [(exp['name'], getattr(exp['metrics'], attr_name)) for exp in experiments]
            
            if smaller_better:
                best_exp = min(values, key=lambda x: x[1])
                worst_exp = max(values, key=lambda x: x[1])
            else:
                if attr_name == 'coverage_95':
                    # 对于覆盖率，找最接近0.95的
                    best_exp = min(values, key=lambda x: abs(x[1] - 0.95))
                    worst_exp = max(values, key=lambda x: abs(x[1] - 0.95))
                else:
                    best_exp = max(values, key=lambda x: x[1])
                    worst_exp = min(values, key=lambda x: x[1])
            
            ranking_data['指标'].append(display_name)
            ranking_data['最佳实验'].append(best_exp[0])
            ranking_data['最佳值'].append(round(best_exp[1], 6))
            ranking_data['最差实验'].append(worst_exp[0])
            ranking_data['最差值'].append(round(worst_exp[1], 6))
        
        return ranking_data
    
    def _analyze_best_practices(self, experiments_data: List[Dict[str, Any]]) -> Dict[str, List]:
        """分析最佳实践"""
        # 找到总体表现最好的实验
        best_experiment = max(experiments_data, 
                            key=lambda x: self._compute_overall_score(x['metrics']))
        
        recommendations = [
            "基于实验结果的最佳实践推荐:",
            f"1. 最佳配置来自实验: {best_experiment['name']}",
            f"2. 该配置的NLL: {best_experiment['metrics'].nll:.4f}",
            f"3. 该配置的Coverage 95%: {best_experiment['metrics'].coverage_95:.4f}",
            f"4. 该配置的Bimodality Score: {best_experiment['metrics'].bimodality_score:.4f}",
            "5. 建议在此基础上进行超参数微调",
            "6. 重点关注物理一致性指标的平衡",
            "7. 在计算资源允许的情况下优先选择该配置"
        ]
        
        return {
            '推荐项目': list(range(1, len(recommendations) + 1)),
            '详细说明': recommendations
        }


def export_metrics_to_excel(metrics: PendulumEvaluationMetrics,
                          experiment_name: str,
                          config: Optional[Dict[str, Any]] = None,
                          output_dir: str = "results/evaluation") -> str:
    """
    便捷的指标导出函数
    
    Args:
        metrics: 评估指标
        experiment_name: 实验名称
        config: 实验配置
        output_dir: 输出目录
        
    Returns:
        导出文件路径
    """
    exporter = MetricsExcelExporter(output_dir)
    return exporter.export_single_experiment(metrics, experiment_name, config)