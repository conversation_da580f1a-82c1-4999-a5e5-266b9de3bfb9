"""
大单摆实验评估指标计算模块

实现完整的统计评估指标：
- NLL (Negative Log-Likelihood): 负对数似然度
- Bimodality Score: 双峰性评分
- Coverage (95%): 95%覆盖率
- 其他专业统计指标
"""

import jax
import jax.numpy as jnp
from jax import vmap, jit
from functools import partial
from typing import Dict, Any, Tuple, Optional
import chex
from jaxtyping import Float, Array
import numpy as np
from scipy import stats
from dataclasses import dataclass


@dataclass
class PendulumEvaluationMetrics:
    """大单摆评估指标数据结构"""
    nll: float                    # 负对数似然度
    bimodality_score: float       # 双峰性评分
    coverage_95: float           # 95%覆盖率
    mse: float                   # 均方误差
    mae: float                   # 平均绝对误差
    circular_mse: float          # 圆形流形上的MSE
    energy_conservation_error: float  # 能量守恒误差
    phase_correlation: float     # 相位相关性
    winding_number_accuracy: float   # 缠绕数准确性
    manifold_consistency: float  # 流形一致性
    computational_time: float    # 计算时间
    memory_usage: float         # 内存使用量


@partial(jit, static_argnums=())
def compute_gaussian_log_likelihood(samples: Float[Array, "N D"], 
                                  mean: Float[Array, "D"], 
                                  cov: Float[Array, "D D"]) -> Float[Array, "N"]:
    """计算高斯分布对数似然度"""
    d = samples.shape[-1]
    diff = samples - mean
    
    # 计算马哈拉诺比斯距离
    cov_inv = jnp.linalg.inv(cov)
    mahalanobis_dist = jnp.sum(diff @ cov_inv * diff, axis=-1)
    
    # 对数似然度
    log_det_cov = jnp.linalg.slogdet(cov)[1]
    log_likelihood = -0.5 * (d * jnp.log(2 * jnp.pi) + log_det_cov + mahalanobis_dist)
    
    return log_likelihood


@partial(jit, static_argnums=())
def compute_nll(predicted_samples: Float[Array, "N D"],
                true_samples: Float[Array, "M D"],
                bandwidth: float = 0.1) -> float:
    """
    计算负对数似然度 (NLL)
    
    使用核密度估计计算预测样本在真实分布下的似然度
    """
    N, D = predicted_samples.shape
    M = true_samples.shape[0]
    
    # 使用高斯核密度估计
    def gaussian_kernel(x, y, h):
        return jnp.exp(-jnp.sum((x - y)**2) / (2 * h**2)) / (h * jnp.sqrt(2 * jnp.pi))**D
    
    # 计算每个预测样本的密度
    def compute_density(x_pred):
        kernels = vmap(lambda x_true: gaussian_kernel(x_pred, x_true, bandwidth))(true_samples)
        return jnp.mean(kernels)
    
    densities = vmap(compute_density)(predicted_samples)
    
    # 避免log(0)
    densities = jnp.maximum(densities, 1e-10)
    log_likelihood = jnp.mean(jnp.log(densities))
    
    return -float(log_likelihood)


@partial(jit, static_argnums=())
def compute_bimodality_score(samples: Float[Array, "N D"]) -> float:
    """
    计算双峰性评分 (Bimodality Score)
    
    基于Hartigan's Dip Test和分布偏度、峰度的综合评估
    """
    N, D = samples.shape
    
    # 对每个维度计算双峰性指标
    bimodality_scores = []
    
    for d in range(D):
        x = samples[:, d]
        
        # 计算偏度和峰度
        mean_x = jnp.mean(x)
        std_x = jnp.std(x)
        skewness = jnp.mean(((x - mean_x) / std_x)**3)
        kurtosis = jnp.mean(((x - mean_x) / std_x)**4)
        
        # Pearson's bimodality coefficient
        # BC = (skew^2 + 1) / (kurtosis + 3*(N-1)^2/((N-2)(N-3)))
        numerator = skewness**2 + 1
        denominator = kurtosis + 3 * (N - 1)**2 / ((N - 2) * (N - 3))
        bc = numerator / denominator
        
        # 双峰性阈值为5/9 ≈ 0.555
        bimodality_indicator = jnp.where(bc > 5/9, 1.0, bc / (5/9))
        bimodality_scores.append(bimodality_indicator)
    
    return float(jnp.mean(jnp.array(bimodality_scores)))


@partial(jit, static_argnums=())
def compute_coverage_95(predicted_samples: Float[Array, "N D"],
                       true_samples: Float[Array, "M D"]) -> float:
    """
    计算95%覆盖率 (Coverage 95%)
    
    测量预测分布能够覆盖95%真实样本的程度
    """
    N, D = predicted_samples.shape
    M = true_samples.shape[0]
    
    # 对每个真实样本，计算其在预测分布中的分位数
    coverage_indicators = []
    
    for i in range(M):
        true_sample = true_samples[i]
        
        # 计算这个真实样本相对于预测分布的Mahalanobis距离
        pred_mean = jnp.mean(predicted_samples, axis=0)
        pred_cov = jnp.cov(predicted_samples.T) + 1e-6 * jnp.eye(D)
        
        diff = true_sample - pred_mean
        cov_inv = jnp.linalg.inv(pred_cov)
        mahalanobis_dist = jnp.sqrt(jnp.sum(diff @ cov_inv * diff))
        
        # 对于多维高斯分布，Mahalanobis距离的平方服从卡方分布
        # 计算95%置信区间
        chi2_95_quantile = 2.4477  # 对于3D（cos_theta, sin_theta, omega）
        
        # 如果样本在95%置信区间内，则被覆盖
        is_covered = mahalanobis_dist**2 <= chi2_95_quantile
        coverage_indicators.append(is_covered)
    
    coverage_rate = jnp.mean(jnp.array(coverage_indicators))
    return float(coverage_rate)


@partial(jit, static_argnums=())
def compute_circular_mse(pred_angles: Float[Array, "N"], 
                        true_angles: Float[Array, "M"]) -> float:
    """计算圆形流形上的均方误差"""
    # 转换为复数表示
    pred_complex = jnp.exp(1j * pred_angles)
    true_complex = jnp.exp(1j * true_angles)
    
    # 计算最近邻匹配的MSE
    pred_mean = jnp.mean(pred_complex)
    true_mean = jnp.mean(true_complex)
    
    # 圆形距离
    circular_diff = jnp.abs(pred_mean - true_mean)
    circular_mse = jnp.real(circular_diff)**2 + jnp.imag(circular_diff)**2
    
    return float(circular_mse)


@partial(jit, static_argnums=())
def compute_energy_conservation_error(trajectories: Float[Array, "N T 3"],
                                    g_over_L: float = 1.0) -> float:
    """计算能量守恒误差"""
    cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
    
    # 重构角度：theta = atan2(sin_theta, cos_theta)
    theta = jnp.arctan2(sin_theta, cos_theta)
    
    # 计算能量：E = 0.5 * omega^2 - g/L * cos(theta)
    kinetic_energy = 0.5 * omega**2
    potential_energy = -g_over_L * cos_theta
    total_energy = kinetic_energy + potential_energy
    
    # 计算每条轨迹的能量变化
    energy_variance = jnp.var(total_energy, axis=1)
    mean_energy_error = jnp.mean(energy_variance)
    
    return float(mean_energy_error)


def compute_comprehensive_metrics(predicted_samples: Float[Array, "N D"],
                                true_samples: Float[Array, "M D"],
                                predicted_trajectories: Optional[Float[Array, "N T D"]] = None,
                                computational_time: float = 0.0,
                                memory_usage: float = 0.0) -> PendulumEvaluationMetrics:
    """
    计算全面的评估指标
    
    Args:
        predicted_samples: 预测样本 [N, D]
        true_samples: 真实样本 [M, D] 
        predicted_trajectories: 预测轨迹 [N, T, D] (可选)
        computational_time: 计算时间
        memory_usage: 内存使用量
        
    Returns:
        PendulumEvaluationMetrics: 完整评估指标
    """
    # 基础统计指标
    pred_mean = jnp.mean(predicted_samples, axis=0)
    true_mean = jnp.mean(true_samples, axis=0)
    
    mse = float(jnp.mean((pred_mean - true_mean)**2))
    mae = float(jnp.mean(jnp.abs(pred_mean - true_mean)))
    
    # 核心评估指标
    nll = compute_nll(predicted_samples, true_samples)
    bimodality_score = compute_bimodality_score(predicted_samples)
    coverage_95 = compute_coverage_95(predicted_samples, true_samples)
    
    # 圆形流形特定指标
    pred_angles = jnp.arctan2(predicted_samples[:, 1], predicted_samples[:, 0])
    true_angles = jnp.arctan2(true_samples[:, 1], true_samples[:, 0])
    circular_mse = compute_circular_mse(pred_angles, true_angles)
    
    # 物理特定指标
    energy_conservation_error = 0.0
    if predicted_trajectories is not None:
        energy_conservation_error = compute_energy_conservation_error(predicted_trajectories)
    
    # 相位相关性
    pred_phase = jnp.angle(jnp.exp(1j * pred_angles))
    true_phase = jnp.angle(jnp.exp(1j * true_angles))
    phase_correlation = float(jnp.corrcoef(pred_phase, true_phase)[0, 1])
    
    # 缠绕数准确性（通过角度变化计算）
    pred_winding = jnp.sum(jnp.diff(pred_angles)) / (2 * jnp.pi)
    true_winding = jnp.sum(jnp.diff(true_angles)) / (2 * jnp.pi)
    winding_number_accuracy = 1.0 - float(jnp.abs(pred_winding - true_winding))
    
    # 流形一致性（单位圆约束）
    pred_norm = jnp.sqrt(predicted_samples[:, 0]**2 + predicted_samples[:, 1]**2)
    manifold_consistency = float(jnp.mean(jnp.abs(pred_norm - 1.0)))
    
    return PendulumEvaluationMetrics(
        nll=nll,
        bimodality_score=bimodality_score,
        coverage_95=coverage_95,
        mse=mse,
        mae=mae,
        circular_mse=circular_mse,
        energy_conservation_error=energy_conservation_error,
        phase_correlation=phase_correlation,
        winding_number_accuracy=winding_number_accuracy,
        manifold_consistency=manifold_consistency,
        computational_time=computational_time,
        memory_usage=memory_usage
    )