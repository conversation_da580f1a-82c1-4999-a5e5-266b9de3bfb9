"""
统计分布可视化模块

提供专业的统计分析可视化功能：
- 分布对比图
- 双峰性分析
- 覆盖率分析
- 统计指标可视化
"""

import matplotlib.pyplot as plt
import numpy as np
import jax.numpy as jnp
from jaxtyping import Float, Array
from typing import Optional, Tuple, List, Dict, Any
import seaborn as sns
from pathlib import Path
import datetime
from scipy import stats
from sklearn.neighbors import KernelDensity

# 设置绘图样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')


class StatisticalVisualizer:
    """统计分析可视化器"""
    
    def __init__(self, output_dir: str = "results/visualization/statistics"):
        """
        初始化统计可视化器
        
        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.figure_size = (15, 10)
        self.dpi = 300
        self.color_palette = sns.color_palette("Set2", 8)
        
    def plot_comprehensive_statistical_analysis(self,
                                               predicted_samples: Float[Array, "N D"],
                                               true_samples: Optional[Float[Array, "M D"]] = None,
                                               experiment_name: str = "statistical_analysis") -> str:
        """
        绘制综合统计分析图
        
        Args:
            predicted_samples: 预测样本 [N, D]
            true_samples: 真实样本 [M, D] (可选)
            experiment_name: 实验名称
            
        Returns:
            保存的文件路径
        """
        N, D = predicted_samples.shape
        
        # 创建大图
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 3, hspace=0.35, wspace=0.3)
        
        # 提取角度和角速度
        cos_theta, sin_theta, omega = predicted_samples[:, 0], predicted_samples[:, 1], predicted_samples[:, 2]
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        if true_samples is not None:
            true_cos_theta, true_sin_theta, true_omega = true_samples[:, 0], true_samples[:, 1], true_samples[:, 2]
            true_theta = jnp.arctan2(true_sin_theta, true_cos_theta)
        
        # 1. 角度分布对比 (左上)
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_distribution_comparison(ax1, theta, true_theta if true_samples is not None else None, 
                                         '角度 θ (rad)', '角度分布对比')
        
        # 2. 角速度分布对比 (中上)
        ax2 = fig.add_subplot(gs[0, 1])
        self._plot_distribution_comparison(ax2, omega, true_omega if true_samples is not None else None,
                                         '角速度 ω (rad/s)', '角速度分布对比')
        
        # 3. 2D联合分布 (右上)
        ax3 = fig.add_subplot(gs[0, 2])
        self._plot_2d_distribution(ax3, theta, omega, true_theta if true_samples is not None else None,
                                 true_omega if true_samples is not None else None)
        
        # 4. 双峰性分析 - 角度 (左中)
        ax4 = fig.add_subplot(gs[1, 0])
        bimodality_theta = self._analyze_bimodality(theta)
        self._plot_bimodality_analysis(ax4, theta, bimodality_theta, '角度双峰性分析')
        
        # 5. 双峰性分析 - 角速度 (中中)
        ax5 = fig.add_subplot(gs[1, 1])
        bimodality_omega = self._analyze_bimodality(omega)
        self._plot_bimodality_analysis(ax5, omega, bimodality_omega, '角速度双峰性分析')
        
        # 6. QQ图 - 正态性检验 (右中)
        ax6 = fig.add_subplot(gs[1, 2])
        self._plot_qq_analysis(ax6, omega, '角速度正态性QQ图')
        
        # 7. 覆盖率分析 (左下)
        ax7 = fig.add_subplot(gs[2, 0])
        if true_samples is not None:
            coverage_analysis = self._compute_coverage_analysis(predicted_samples, true_samples)
            self._plot_coverage_analysis(ax7, coverage_analysis)
        else:
            ax7.text(0.5, 0.5, '需要真实样本\n进行覆盖率分析', ha='center', va='center', transform=ax7.transAxes)
            ax7.set_title('覆盖率分析')
        
        # 8. 统计指标汇总 (中下)
        ax8 = fig.add_subplot(gs[2, 1])
        stats_summary = self._compute_statistical_summary(predicted_samples, true_samples)
        self._plot_statistics_summary(ax8, stats_summary)
        
        # 9. 分位数分析 (右下)
        ax9 = fig.add_subplot(gs[2, 2])
        self._plot_quantile_analysis(ax9, predicted_samples, true_samples)
        
        # 10. 详细统计表 (底部跨列)
        ax10 = fig.add_subplot(gs[3, :])
        ax10.axis('off')
        
        detailed_stats = self._compute_detailed_statistics(predicted_samples, true_samples)
        ax10.text(0.05, 0.95, detailed_stats, transform=ax10.transAxes, fontsize=11,
                 verticalalignment='top', fontfamily='monospace',
                 bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # 添加总标题
        fig.suptitle(f'统计分析综合报告 - {experiment_name}', fontsize=16, fontweight='bold')
        
        # 保存图片
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{experiment_name}_statistical_analysis_{timestamp}.png"
        filepath = self.output_dir / filename
        
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 统计分析图已保存至: {filepath}")
        return str(filepath)
    
    def _plot_distribution_comparison(self, ax, pred_data, true_data, xlabel, title):
        """绘制分布对比"""
        # 预测分布
        ax.hist(pred_data, bins=50, alpha=0.7, density=True, color=self.color_palette[0], 
                label='预测分布', edgecolor='black', linewidth=0.5)
        
        # 核密度估计
        kde_x = np.linspace(np.min(pred_data), np.max(pred_data), 200)
        kde = KernelDensity(bandwidth=0.1).fit(np.array(pred_data).reshape(-1, 1))
        kde_pred = np.exp(kde.score_samples(kde_x.reshape(-1, 1)))
        ax.plot(kde_x, kde_pred, color=self.color_palette[0], linewidth=2, label='预测KDE')
        
        # 真实分布（如果有）
        if true_data is not None:
            ax.hist(true_data, bins=50, alpha=0.5, density=True, color='red', 
                    label='真实分布', edgecolor='darkred', linewidth=0.5)
            
            kde_true = KernelDensity(bandwidth=0.1).fit(np.array(true_data).reshape(-1, 1))
            kde_true_pred = np.exp(kde_true.score_samples(kde_x.reshape(-1, 1)))
            ax.plot(kde_x, kde_true_pred, color='red', linewidth=2, linestyle='--', label='真实KDE')
        
        ax.set_xlabel(xlabel)
        ax.set_ylabel('概率密度')
        ax.set_title(title)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_2d_distribution(self, ax, theta, omega, true_theta, true_omega):
        """绘制2D联合分布"""
        # 预测分布的2D直方图
        ax.hist2d(theta, omega, bins=30, alpha=0.7, cmap='Blues', density=True)
        
        # 真实分布的散点图（如果有）
        if true_theta is not None and true_omega is not None:
            ax.scatter(true_theta, true_omega, c='red', alpha=0.6, s=10, label='真实样本')
            ax.legend()
        
        ax.set_xlabel('角度 θ (rad)')
        ax.set_ylabel('角速度 ω (rad/s)')
        ax.set_title('θ-ω 联合分布')
    
    def _analyze_bimodality(self, data):
        """分析双峰性"""
        data_array = np.array(data)
        
        # 计算偏度和峰度
        skewness = stats.skew(data_array)
        kurtosis = stats.kurtosis(data_array)
        
        # Pearson's bimodality coefficient
        n = len(data_array)
        bc = (skewness**2 + 1) / (kurtosis + 3 * (n-1)**2 / ((n-2) * (n-3)))
        
        return {
            'bimodality_coefficient': bc,
            'skewness': skewness,
            'kurtosis': kurtosis,
            'is_bimodal': bc > 5/9
        }
    
    def _plot_bimodality_analysis(self, ax, data, bimodality_info, title):
        """绘制双峰性分析"""
        # 直方图和密度曲线
        n, bins, patches = ax.hist(data, bins=40, alpha=0.7, density=True, 
                                  color=self.color_palette[2], edgecolor='black')
        
        # KDE曲线
        kde_x = np.linspace(np.min(data), np.max(data), 200)
        kde = KernelDensity(bandwidth=0.15).fit(np.array(data).reshape(-1, 1))
        kde_pred = np.exp(kde.score_samples(kde_x.reshape(-1, 1)))
        ax.plot(kde_x, kde_pred, color='darkblue', linewidth=3, label='KDE')
        
        # 寻找峰值
        from scipy.signal import find_peaks
        peaks, _ = find_peaks(kde_pred, height=np.max(kde_pred)*0.1)
        for peak in peaks:
            ax.axvline(kde_x[peak], color='red', linestyle='--', alpha=0.8)
        
        ax.set_xlabel('数值')
        ax.set_ylabel('概率密度')
        ax.set_title(f"{title}\nBC={bimodality_info['bimodality_coefficient']:.3f}, 峰数={len(peaks)}")
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        info_text = f"偏度: {bimodality_info['skewness']:.3f}\n峰度: {bimodality_info['kurtosis']:.3f}"
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def _plot_qq_analysis(self, ax, data, title):
        """绘制QQ图分析"""
        # 正态QQ图
        stats.probplot(np.array(data), dist="norm", plot=ax)
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        
        # 计算R²
        sorted_data = np.sort(data)
        normal_quantiles = stats.norm.ppf(np.linspace(0.01, 0.99, len(sorted_data)))
        r2 = np.corrcoef(sorted_data, normal_quantiles)[0, 1]**2
        
        ax.text(0.05, 0.95, f'R² = {r2:.4f}', transform=ax.transAxes, fontsize=12,
                bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8))
    
    def _compute_coverage_analysis(self, predicted_samples, true_samples):
        """计算覆盖率分析"""
        confidence_levels = [0.5, 0.68, 0.90, 0.95, 0.99]
        coverage_results = {}
        
        for conf_level in confidence_levels:
            # 计算预测分布的置信区间
            pred_mean = jnp.mean(predicted_samples, axis=0)
            pred_cov = jnp.cov(predicted_samples.T) + 1e-6 * jnp.eye(predicted_samples.shape[1])
            
            # 计算真实样本在置信区间内的比例
            covered_count = 0
            for true_sample in true_samples:
                diff = true_sample - pred_mean
                cov_inv = jnp.linalg.inv(pred_cov)
                mahalanobis_dist_sq = jnp.sum(diff @ cov_inv * diff)
                
                # 对于3D多元正态分布的置信区间
                chi2_quantile = stats.chi2.ppf(conf_level, df=3)
                if mahalanobis_dist_sq <= chi2_quantile:
                    covered_count += 1
            
            actual_coverage = covered_count / len(true_samples)
            coverage_results[conf_level] = {
                'expected': conf_level,
                'actual': actual_coverage,
                'error': abs(actual_coverage - conf_level)
            }
        
        return coverage_results
    
    def _plot_coverage_analysis(self, ax, coverage_analysis):
        """绘制覆盖率分析"""
        conf_levels = list(coverage_analysis.keys())
        expected = [coverage_analysis[cl]['expected'] for cl in conf_levels]
        actual = [coverage_analysis[cl]['actual'] for cl in conf_levels]
        
        x = np.arange(len(conf_levels))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, expected, width, label='期望覆盖率', alpha=0.8, color=self.color_palette[3])
        bars2 = ax.bar(x + width/2, actual, width, label='实际覆盖率', alpha=0.8, color=self.color_palette[4])
        
        ax.set_xlabel('置信水平')
        ax.set_ylabel('覆盖率')
        ax.set_title('覆盖率分析')
        ax.set_xticks(x)
        ax.set_xticklabels([f'{cl:.0%}' for cl in conf_levels])
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (exp, act) in enumerate(zip(expected, actual)):
            ax.text(i - width/2, exp + 0.01, f'{exp:.3f}', ha='center', fontsize=9)
            ax.text(i + width/2, act + 0.01, f'{act:.3f}', ha='center', fontsize=9)
    
    def _compute_statistical_summary(self, predicted_samples, true_samples):
        """计算统计汇总"""
        N, D = predicted_samples.shape
        
        # 基础统计
        pred_mean = jnp.mean(predicted_samples, axis=0)
        pred_std = jnp.std(predicted_samples, axis=0)
        
        summary = {
            'sample_size': N,
            'dimensions': D,
            'pred_mean': pred_mean,
            'pred_std': pred_std
        }
        
        if true_samples is not None:
            true_mean = jnp.mean(true_samples, axis=0)
            true_std = jnp.std(true_samples, axis=0)
            
            # 均值差异
            mean_diff = jnp.abs(pred_mean - true_mean)
            # 标准差差异
            std_diff = jnp.abs(pred_std - true_std)
            
            summary.update({
                'true_mean': true_mean,
                'true_std': true_std,
                'mean_diff': mean_diff,
                'std_diff': std_diff
            })
        
        return summary
    
    def _plot_statistics_summary(self, ax, stats_summary):
        """绘制统计汇总"""
        ax.axis('off')
        
        # 维度标签
        dim_labels = ['cos θ', 'sin θ', 'ω']
        
        summary_text = f"统计汇总 (N={stats_summary['sample_size']}):\n"
        summary_text += "=" * 40 + "\n"
        
        for i, label in enumerate(dim_labels):
            summary_text += f"{label}:\n"
            summary_text += f"  预测: μ={stats_summary['pred_mean'][i]:.4f}, σ={stats_summary['pred_std'][i]:.4f}\n"
            
            if 'true_mean' in stats_summary:
                summary_text += f"  真实: μ={stats_summary['true_mean'][i]:.4f}, σ={stats_summary['true_std'][i]:.4f}\n"
                summary_text += f"  差异: Δμ={stats_summary['mean_diff'][i]:.4f}, Δσ={stats_summary['std_diff'][i]:.4f}\n"
            
            summary_text += "\n"
        
        ax.text(0.05, 0.95, summary_text, transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    def _plot_quantile_analysis(self, ax, predicted_samples, true_samples):
        """绘制分位数分析"""
        # 计算各维度的分位数
        quantiles = [0.05, 0.25, 0.5, 0.75, 0.95]
        dim_labels = ['cos θ', 'sin θ', 'ω']
        
        pred_quantiles = jnp.quantile(predicted_samples, quantiles, axis=0)
        
        x = np.arange(len(dim_labels))
        width = 0.15
        
        colors = plt.cm.viridis(np.linspace(0, 1, len(quantiles)))
        
        for i, q in enumerate(quantiles):
            offset = (i - len(quantiles)//2) * width
            bars = ax.bar(x + offset, pred_quantiles[i], width, 
                         label=f'{q:.0%}分位数', alpha=0.8, color=colors[i])
        
        if true_samples is not None:
            true_quantiles = jnp.quantile(true_samples, quantiles, axis=0)
            for i, q in enumerate(quantiles):
                offset = (i - len(quantiles)//2) * width
                ax.scatter(x + offset, true_quantiles[i], 
                          color='red', s=60, marker='x', linewidth=3, zorder=5)
        
        ax.set_xlabel('维度')
        ax.set_ylabel('分位数值')
        ax.set_title('分位数分析')
        ax.set_xticks(x)
        ax.set_xticklabels(dim_labels)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _compute_detailed_statistics(self, predicted_samples, true_samples):
        """计算详细统计信息"""
        N, D = predicted_samples.shape
        
        # 基础统计
        cos_theta, sin_theta, omega = predicted_samples[:, 0], predicted_samples[:, 1], predicted_samples[:, 2]
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        # 角度统计
        theta_stats = {
            'mean': float(jnp.mean(theta)),
            'std': float(jnp.std(theta)),
            'min': float(jnp.min(theta)),
            'max': float(jnp.max(theta)),
            'range': float(jnp.max(theta) - jnp.min(theta))
        }
        
        # 角速度统计
        omega_stats = {
            'mean': float(jnp.mean(omega)),
            'std': float(jnp.std(omega)),
            'min': float(jnp.min(omega)),
            'max': float(jnp.max(omega)),
            'range': float(jnp.max(omega) - jnp.min(omega))
        }
        
        # 流形约束检查
        manifold_error = float(jnp.mean(jnp.abs(cos_theta**2 + sin_theta**2 - 1.0)))
        
        detailed_text = f"""
详细统计信息 (N={N}):
{'='*50}

角度统计:
  • 均值: {theta_stats['mean']:.4f} rad ({np.degrees(theta_stats['mean']):.2f}°)
  • 标准差: {theta_stats['std']:.4f} rad ({np.degrees(theta_stats['std']):.2f}°)
  • 范围: [{theta_stats['min']:.3f}, {theta_stats['max']:.3f}] rad
  • 跨度: {theta_stats['range']:.4f} rad ({np.degrees(theta_stats['range']):.2f}°)

角速度统计:
  • 均值: {omega_stats['mean']:.4f} rad/s
  • 标准差: {omega_stats['std']:.4f} rad/s  
  • 范围: [{omega_stats['min']:.3f}, {omega_stats['max']:.3f}] rad/s
  • 跨度: {omega_stats['range']:.4f} rad/s

流形约束:
  • 约束误差: {manifold_error:.6f}
  • 约束满足度: {(1-manifold_error)*100:.3f}%
        """
        
        if true_samples is not None:
            # 与真实样本的比较
            true_cos_theta, true_sin_theta, true_omega = true_samples[:, 0], true_samples[:, 1], true_samples[:, 2]
            true_theta = jnp.arctan2(true_sin_theta, true_cos_theta)
            
            # KL散度估计
            try:
                from scipy.stats import entropy
                # 简化的KL散度估计
                theta_hist_pred, bins = np.histogram(theta, bins=50, density=True)
                theta_hist_true, _ = np.histogram(true_theta, bins=bins, density=True)
                
                # 避免0值
                theta_hist_pred = np.maximum(theta_hist_pred, 1e-10)
                theta_hist_true = np.maximum(theta_hist_true, 1e-10)
                
                kl_div = entropy(theta_hist_pred, theta_hist_true)
                
                detailed_text += f"""
与真实样本比较:
  • 角度KL散度: {kl_div:.6f}
  • 角度均值差: {float(jnp.abs(jnp.mean(theta) - jnp.mean(true_theta))):.6f} rad
  • 角速度均值差: {float(jnp.abs(jnp.mean(omega) - jnp.mean(true_omega))):.6f} rad/s
                """
            except:
                detailed_text += "\n与真实样本比较: 计算中..."
        
        return detailed_text


def plot_distribution_comparison(predicted_samples: Float[Array, "N D"],
                               true_samples: Optional[Float[Array, "M D"]] = None,
                               title: str = "分布对比",
                               output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制分布对比图的便捷函数
    
    Args:
        predicted_samples: 预测样本 [N, D]
        true_samples: 真实样本 [M, D] (可选)
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    visualizer = StatisticalVisualizer()
    return visualizer.plot_comprehensive_statistical_analysis(
        predicted_samples, true_samples, title.replace(" ", "_"))


def plot_bimodality_analysis(data: Float[Array, "N"],
                           title: str = "双峰性分析", 
                           output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制双峰性分析图的便捷函数
    
    Args:
        data: 数据 [N]
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    visualizer = StatisticalVisualizer()
    
    plt.figure(figsize=(10, 6))
    ax = plt.gca()
    
    bimodality_info = visualizer._analyze_bimodality(data)
    visualizer._plot_bimodality_analysis(ax, data, bimodality_info, title)
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.show()
        return None


def plot_coverage_analysis(predicted_samples: Float[Array, "N D"],
                         true_samples: Float[Array, "M D"],
                         title: str = "覆盖率分析",
                         output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制覆盖率分析图的便捷函数
    
    Args:
        predicted_samples: 预测样本 [N, D]
        true_samples: 真实样本 [M, D]
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    visualizer = StatisticalVisualizer()
    
    plt.figure(figsize=(10, 6))
    ax = plt.gca()
    
    coverage_analysis = visualizer._compute_coverage_analysis(predicted_samples, true_samples)
    visualizer._plot_coverage_analysis(ax, coverage_analysis)
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.show()
        return None