"""
误差分析可视化模块

提供专业的误差分析可视化功能：
- 误差演化图
- 收敛性分析
- 流形一致性检查
- 数值稳定性分析
"""

import matplotlib.pyplot as plt
import numpy as np
import jax.numpy as jnp
from jaxtyping import Float, Array
from typing import Optional, Tuple, List, Dict, Any
import seaborn as sns
from pathlib import Path
import datetime

# 设置绘图样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')


class ErrorAnalysisVisualizer:
    """误差分析可视化器"""
    
    def __init__(self, output_dir: str = "results/visualization/error_analysis"):
        """
        初始化误差分析可视化器
        
        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.figure_size = (15, 10)
        self.dpi = 300
        self.color_palette = sns.color_palette("viridis", 8)
        
    def plot_comprehensive_error_analysis(self,
                                        trajectories: Float[Array, "N T 3"],
                                        true_trajectories: Optional[Float[Array, "M T 3"]] = None,
                                        time_points: Optional[Float[Array, "T"]] = None,
                                        experiment_name: str = "error_analysis",
                                        g_over_L: float = 1.0) -> str:
        """
        绘制综合误差分析图
        
        Args:
            trajectories: 预测轨迹 [N, T, 3]
            true_trajectories: 真实轨迹 [M, T, 3] (可选)
            time_points: 时间点 [T]
            experiment_name: 实验名称
            g_over_L: 重力加速度与长度比值
            
        Returns:
            保存的文件路径
        """
        N, T, D = trajectories.shape
        
        if time_points is None:
            time_points = jnp.linspace(0, 10, T)
        
        # 创建大图
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 3, hspace=0.35, wspace=0.3)
        
        # 提取数据
        cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        # 1. 流形约束误差演化 (左上)
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_manifold_constraint_error(ax1, cos_theta, sin_theta, time_points)
        
        # 2. 能量守恒误差演化 (中上)
        ax2 = fig.add_subplot(gs[0, 1])
        self._plot_energy_conservation_error(ax2, trajectories, time_points, g_over_L)
        
        # 3. 轨迹误差（如果有真实轨迹） (右上)
        ax3 = fig.add_subplot(gs[0, 2])
        if true_trajectories is not None:
            self._plot_trajectory_error(ax3, trajectories, true_trajectories, time_points)
        else:
            ax3.text(0.5, 0.5, '需要真实轨迹\n进行误差分析', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('轨迹误差分析')
        
        # 4. 数值稳定性分析 (左中)
        ax4 = fig.add_subplot(gs[1, 0])
        self._plot_numerical_stability(ax4, trajectories, time_points)
        
        # 5. 角度误差分布 (中中)
        ax5 = fig.add_subplot(gs[1, 1])
        if true_trajectories is not None:
            self._plot_angular_error_distribution(ax5, trajectories, true_trajectories)
        else:
            ax5.text(0.5, 0.5, '需要真实轨迹\n进行角度误差分析', ha='center', va='center', transform=ax5.transAxes)
            ax5.set_title('角度误差分布')
        
        # 6. 相位误差分析 (右中)
        ax6 = fig.add_subplot(gs[1, 2])
        if true_trajectories is not None:
            self._plot_phase_error_analysis(ax6, trajectories, true_trajectories, time_points)
        else:
            ax6.text(0.5, 0.5, '需要真实轨迹\n进行相位误差分析', ha='center', va='center', transform=ax6.transAxes)
            ax6.set_title('相位误差分析')
        
        # 7. 收敛性分析 (左下)
        ax7 = fig.add_subplot(gs[2, 0])
        self._plot_convergence_analysis(ax7, trajectories, time_points)
        
        # 8. 误差频谱分析 (中下)
        ax8 = fig.add_subplot(gs[2, 1])
        if true_trajectories is not None:
            self._plot_error_spectrum_analysis(ax8, trajectories, true_trajectories, time_points)
        else:
            ax8.text(0.5, 0.5, '需要真实轨迹\n进行频谱分析', ha='center', va='center', transform=ax8.transAxes)
            ax8.set_title('误差频谱分析')
        
        # 9. 误差统计汇总 (右下)
        ax9 = fig.add_subplot(gs[2, 2])
        error_stats = self._compute_error_statistics(trajectories, true_trajectories, g_over_L)
        self._plot_error_statistics_summary(ax9, error_stats)
        
        # 10. 详细误差报告 (底部跨列)
        ax10 = fig.add_subplot(gs[3, :])
        ax10.axis('off')
        
        detailed_report = self._generate_detailed_error_report(trajectories, true_trajectories, time_points, g_over_L)
        ax10.text(0.05, 0.95, detailed_report, transform=ax10.transAxes, fontsize=10,
                 verticalalignment='top', fontfamily='monospace',
                 bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
        
        # 添加总标题
        fig.suptitle(f'综合误差分析报告 - {experiment_name}', fontsize=16, fontweight='bold')
        
        # 保存图片
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{experiment_name}_error_analysis_{timestamp}.png"
        filepath = self.output_dir / filename
        
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 误差分析图已保存至: {filepath}")
        return str(filepath)
    
    def _plot_manifold_constraint_error(self, ax, cos_theta, sin_theta, time_points):
        """绘制流形约束误差"""
        manifold_constraint = cos_theta**2 + sin_theta**2
        constraint_error = jnp.abs(manifold_constraint - 1.0)
        
        N = min(constraint_error.shape[0], 10)  # 最多显示10条轨迹
        
        for i in range(N):
            ax.semilogy(time_points, constraint_error[i], alpha=0.7, linewidth=1.5)
        
        # 平均误差
        mean_error = jnp.mean(constraint_error, axis=0)
        ax.semilogy(time_points, mean_error, 'k-', linewidth=3, label='平均误差')
        
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('流形约束误差 (log scale)')
        ax.set_title('流形约束误差演化\n|cos²θ + sin²θ - 1|')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        final_error = float(jnp.mean(constraint_error[:, -1]))
        ax.text(0.02, 0.98, f'最终误差: {final_error:.2e}', transform=ax.transAxes,
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def _plot_energy_conservation_error(self, ax, trajectories, time_points, g_over_L):
        """绘制能量守恒误差"""
        cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
        
        # 计算能量
        kinetic_energy = 0.5 * omega**2
        potential_energy = -g_over_L * cos_theta
        total_energy = kinetic_energy + potential_energy
        
        N = min(total_energy.shape[0], 10)
        
        # 计算能量守恒误差（相对于初始能量）
        for i in range(N):
            initial_energy = total_energy[i, 0]
            energy_error = jnp.abs(total_energy[i] - initial_energy)
            ax.semilogy(time_points, energy_error, alpha=0.7, linewidth=1.5)
        
        # 平均误差
        mean_energy_error = []
        for i in range(total_energy.shape[0]):
            initial_energy = total_energy[i, 0]
            energy_error = jnp.abs(total_energy[i] - initial_energy)
            mean_energy_error.append(energy_error)
        
        mean_error = jnp.mean(jnp.array(mean_energy_error), axis=0)
        ax.semilogy(time_points, mean_error, 'k-', linewidth=3, label='平均误差')
        
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('能量守恒误差 (log scale)')
        ax.set_title('能量守恒误差演化\n|E(t) - E(0)|')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        final_error = float(mean_error[-1])
        ax.text(0.02, 0.98, f'最终误差: {final_error:.2e}', transform=ax.transAxes,
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def _plot_trajectory_error(self, ax, pred_trajectories, true_trajectories, time_points):
        """绘制轨迹误差"""
        N = min(pred_trajectories.shape[0], true_trajectories.shape[0], 10)
        
        # 计算各维度的L2误差
        errors = []
        dim_labels = ['cos θ', 'sin θ', 'ω']
        colors = ['red', 'blue', 'green']
        
        for d in range(3):
            dim_errors = []
            for i in range(N):
                error = jnp.abs(pred_trajectories[i, :, d] - true_trajectories[i, :, d])
                dim_errors.append(error)
                if i < 3:  # 只显示前3条轨迹的个别误差
                    ax.plot(time_points, error, color=colors[d], alpha=0.3, linewidth=1)
            
            # 平均误差
            mean_error = jnp.mean(jnp.array(dim_errors), axis=0)
            ax.plot(time_points, mean_error, color=colors[d], linewidth=3, label=f'{dim_labels[d]} 平均误差')
            errors.append(mean_error)
        
        # 总体L2误差
        total_l2_error = jnp.sqrt(jnp.sum(jnp.array(errors)**2, axis=0))
        ax.plot(time_points, total_l2_error, 'k--', linewidth=3, label='总体L2误差')
        
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('绝对误差')
        ax.set_title('轨迹误差演化')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')
    
    def _plot_numerical_stability(self, ax, trajectories, time_points):
        """绘制数值稳定性分析"""
        cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
        
        # 计算轨迹间的标准差作为稳定性指标
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        theta_std = jnp.std(theta, axis=0)
        omega_std = jnp.std(omega, axis=0)
        
        ax.plot(time_points, theta_std, label='角度标准差', linewidth=2)
        ax.plot(time_points, omega_std, label='角速度标准差', linewidth=2)
        
        # 组合稳定性指标
        combined_stability = jnp.sqrt(theta_std**2 + omega_std**2)
        ax.plot(time_points, combined_stability, 'k--', label='组合稳定性指标', linewidth=3)
        
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('标准差')
        ax.set_title('数值稳定性分析')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加趋势线
        z = np.polyfit(time_points, combined_stability, 1)
        p = np.poly1d(z)
        ax.plot(time_points, p(time_points), "r:", alpha=0.8, label=f'趋势 (斜率={z[0]:.3e})')
        ax.legend()
    
    def _plot_angular_error_distribution(self, ax, pred_trajectories, true_trajectories):
        """绘制角度误差分布"""
        N = min(pred_trajectories.shape[0], true_trajectories.shape[0])
        
        # 计算角度
        pred_theta = jnp.arctan2(pred_trajectories[:N, :, 1], pred_trajectories[:N, :, 0])
        true_theta = jnp.arctan2(true_trajectories[:N, :, 1], true_trajectories[:N, :, 0])
        
        # 计算角度误差（考虑周期性）
        angle_diff = pred_theta - true_theta
        angle_error = jnp.minimum(jnp.abs(angle_diff), 2*jnp.pi - jnp.abs(angle_diff))
        
        # 展平数据
        angle_error_flat = angle_error.flatten()
        
        # 绘制直方图
        ax.hist(angle_error_flat, bins=50, alpha=0.7, density=True, color=self.color_palette[0])
        
        # 添加统计线
        mean_error = float(jnp.mean(angle_error_flat))
        median_error = float(jnp.median(angle_error_flat))
        
        ax.axvline(mean_error, color='red', linestyle='--', linewidth=2, label=f'均值: {mean_error:.4f}')
        ax.axvline(median_error, color='blue', linestyle='--', linewidth=2, label=f'中位数: {median_error:.4f}')
        
        ax.set_xlabel('角度误差 (rad)')
        ax.set_ylabel('概率密度')
        ax.set_title('角度误差分布')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_phase_error_analysis(self, ax, pred_trajectories, true_trajectories, time_points):
        """绘制相位误差分析"""
        N = min(pred_trajectories.shape[0], true_trajectories.shape[0], 5)
        
        # 计算相位空间误差
        for i in range(N):
            pred_theta = jnp.arctan2(pred_trajectories[i, :, 1], pred_trajectories[i, :, 0])
            true_theta = jnp.arctan2(true_trajectories[i, :, 1], true_trajectories[i, :, 0])
            
            pred_omega = pred_trajectories[i, :, 2]
            true_omega = true_trajectories[i, :, 2]
            
            # 计算相位空间距离
            phase_error = jnp.sqrt((pred_theta - true_theta)**2 + (pred_omega - true_omega)**2)
            ax.plot(time_points, phase_error, alpha=0.7, linewidth=1.5)
        
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('相位空间误差')
        ax.set_title('相位空间误差演化')
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')
    
    def _plot_convergence_analysis(self, ax, trajectories, time_points):
        """绘制收敛性分析"""
        N, T, D = trajectories.shape
        
        # 计算每个时间步的轨迹集合的"收敛性"
        # 使用轨迹间平均距离作为收敛指标
        convergence_metric = []
        
        for t in range(T):
            # 计算时刻t所有轨迹的两两距离平均值
            current_states = trajectories[:, t, :]  # [N, 3]
            
            distances = []
            for i in range(N):
                for j in range(i+1, N):
                    dist = jnp.linalg.norm(current_states[i] - current_states[j])
                    distances.append(dist)
            
            avg_distance = jnp.mean(jnp.array(distances)) if distances else 0.0
            convergence_metric.append(avg_distance)
        
        convergence_metric = jnp.array(convergence_metric)
        
        ax.plot(time_points, convergence_metric, linewidth=3, color=self.color_palette[2])
        
        # 添加趋势分析
        if len(time_points) > 2:
            z = np.polyfit(time_points, convergence_metric, 1)
            p = np.poly1d(z)
            ax.plot(time_points, p(time_points), "r--", alpha=0.8, label=f'线性趋势 (斜率={z[0]:.3e})')
        
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('轨迹分散度')
        ax.set_title('收敛性分析\n(轨迹间平均距离)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 判断收敛趋势
        if len(time_points) > 2 and z[0] < 0:
            trend_text = "收敛趋势"
            color = 'green'
        elif len(time_points) > 2 and z[0] > 0:
            trend_text = "发散趋势"
            color = 'red'
        else:
            trend_text = "稳定状态"
            color = 'blue'
        
        ax.text(0.02, 0.98, trend_text, transform=ax.transAxes, fontsize=12,
                verticalalignment='top', color=color, fontweight='bold',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def _plot_error_spectrum_analysis(self, ax, pred_trajectories, true_trajectories, time_points):
        """绘制误差频谱分析"""
        N = min(pred_trajectories.shape[0], true_trajectories.shape[0])
        
        # 计算角度误差的频谱
        pred_theta = jnp.arctan2(pred_trajectories[:N, :, 1], pred_trajectories[:N, :, 0])
        true_theta = jnp.arctan2(true_trajectories[:N, :, 1], true_trajectories[:N, :, 0])
        
        angle_error = pred_theta - true_theta
        
        # 对每条轨迹计算FFT
        dt = float(time_points[1] - time_points[0])
        frequencies = np.fft.fftfreq(len(time_points), dt)
        
        power_spectra = []
        for i in range(min(N, 5)):  # 最多分析5条轨迹
            fft_error = np.fft.fft(angle_error[i])
            power_spectrum = np.abs(fft_error)**2
            power_spectra.append(power_spectrum)
        
        # 平均功率谱
        avg_power_spectrum = np.mean(power_spectra, axis=0)
        
        # 只显示正频率部分
        pos_freq_idx = frequencies > 0
        ax.loglog(frequencies[pos_freq_idx], avg_power_spectrum[pos_freq_idx], linewidth=2)
        
        ax.set_xlabel('频率 (Hz)')
        ax.set_ylabel('功率谱密度')
        ax.set_title('角度误差频谱分析')
        ax.grid(True, alpha=0.3)
        
        # 找到主导频率
        dominant_freq_idx = np.argmax(avg_power_spectrum[pos_freq_idx])
        dominant_freq = frequencies[pos_freq_idx][dominant_freq_idx]
        ax.axvline(dominant_freq, color='red', linestyle='--', 
                  label=f'主导频率: {dominant_freq:.3f} Hz')
        ax.legend()
    
    def _plot_error_statistics_summary(self, ax, error_stats):
        """绘制误差统计汇总"""
        ax.axis('off')
        
        if error_stats['has_true_data']:
            summary_text = f"""
误差统计汇总:
{'='*30}

轨迹误差:
  • 角度RMSE: {error_stats['angle_rmse']:.6f} rad
  • 角速度RMSE: {error_stats['omega_rmse']:.6f} rad/s
  • 总体L2误差: {error_stats['total_l2_error']:.6f}

物理约束误差:
  • 能量守恒误差: {error_stats['energy_error']:.2e}
  • 流形约束误差: {error_stats['manifold_error']:.2e}

数值稳定性:
  • 角度稳定性: {error_stats['theta_stability']:.6f}
  • 角速度稳定性: {error_stats['omega_stability']:.6f}
            """
        else:
            summary_text = f"""
误差统计汇总:
{'='*30}

物理约束误差:
  • 能量守恒误差: {error_stats['energy_error']:.2e}
  • 流形约束误差: {error_stats['manifold_error']:.2e}

数值稳定性:
  • 角度稳定性: {error_stats['theta_stability']:.6f} 
  • 角速度稳定性: {error_stats['omega_stability']:.6f}

注: 无真实轨迹数据，
无法计算轨迹误差
            """
        
        ax.text(0.05, 0.95, summary_text, transform=ax.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))
    
    def _compute_error_statistics(self, trajectories, true_trajectories, g_over_L):
        """计算误差统计"""
        N, T, D = trajectories.shape
        
        cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        # 物理约束误差
        manifold_constraint = cos_theta**2 + sin_theta**2
        manifold_error = float(jnp.mean(jnp.abs(manifold_constraint - 1.0)))
        
        # 能量守恒误差
        kinetic_energy = 0.5 * omega**2
        potential_energy = -g_over_L * cos_theta
        total_energy = kinetic_energy + potential_energy
        
        energy_errors = []
        for i in range(N):
            initial_energy = total_energy[i, 0]
            energy_error = jnp.var(total_energy[i])  # 能量变化的方差
            energy_errors.append(energy_error)
        
        energy_error = float(jnp.mean(jnp.array(energy_errors)))
        
        # 数值稳定性
        theta_stability = float(jnp.mean(jnp.std(theta, axis=0)))
        omega_stability = float(jnp.mean(jnp.std(omega, axis=0)))
        
        error_stats = {
            'has_true_data': true_trajectories is not None,
            'manifold_error': manifold_error,
            'energy_error': energy_error,
            'theta_stability': theta_stability,
            'omega_stability': omega_stability
        }
        
        # 如果有真实轨迹，计算轨迹误差
        if true_trajectories is not None:
            M = min(N, true_trajectories.shape[0])
            
            pred_theta = jnp.arctan2(trajectories[:M, :, 1], trajectories[:M, :, 0])
            true_theta = jnp.arctan2(true_trajectories[:M, :, 1], true_trajectories[:M, :, 0])
            
            # 角度RMSE
            angle_error = pred_theta - true_theta
            angle_rmse = float(jnp.sqrt(jnp.mean(angle_error**2)))
            
            # 角速度RMSE
            omega_error = trajectories[:M, :, 2] - true_trajectories[:M, :, 2]
            omega_rmse = float(jnp.sqrt(jnp.mean(omega_error**2)))
            
            # 总体L2误差
            total_error = trajectories[:M] - true_trajectories[:M]
            total_l2_error = float(jnp.sqrt(jnp.mean(jnp.sum(total_error**2, axis=-1))))
            
            error_stats.update({
                'angle_rmse': angle_rmse,
                'omega_rmse': omega_rmse,
                'total_l2_error': total_l2_error
            })
        
        return error_stats
    
    def _generate_detailed_error_report(self, trajectories, true_trajectories, time_points, g_over_L):
        """生成详细误差报告"""
        N, T, D = trajectories.shape
        
        error_stats = self._compute_error_statistics(trajectories, true_trajectories, g_over_L)
        
        report = f"""
详细误差分析报告 (N={N}, T={T}):
{'='*60}

1. 数值精度分析:
   • 流形约束误差: {error_stats['manifold_error']:.2e}
     - 理论值: cos²θ + sin²θ = 1
     - 实际偏差: 平均 {error_stats['manifold_error']:.2e}
   
   • 能量守恒误差: {error_stats['energy_error']:.2e}
     - 单摆能量应该守恒
     - 实际能量方差: {error_stats['energy_error']:.2e}

2. 数值稳定性分析:
   • 角度稳定性指标: {error_stats['theta_stability']:.6f}
   • 角速度稳定性指标: {error_stats['omega_stability']:.6f}
   • 总体稳定性评估: {'良好' if max(error_stats['theta_stability'], error_stats['omega_stability']) < 0.1 else '需要关注'}

3. 计算质量评估:
   • 流形约束满足度: {(1-error_stats['manifold_error'])*100:.4f}%
   • 物理定律保持度: {(1-error_stats['energy_error']/max(error_stats['energy_error'], 1e-10))*100:.2f}%
        """
        
        if error_stats['has_true_data']:
            report += f"""

4. 预测精度分析:
   • 角度预测RMSE: {error_stats['angle_rmse']:.6f} rad ({np.degrees(error_stats['angle_rmse']):.3f}°)
   • 角速度预测RMSE: {error_stats['omega_rmse']:.6f} rad/s
   • 总体L2误差: {error_stats['total_l2_error']:.6f}
   
   • 相对误差水平: {'高精度' if error_stats['total_l2_error'] < 0.01 else '中等精度' if error_stats['total_l2_error'] < 0.1 else '需要改进'}
            """
        else:
            report += """

4. 预测精度分析:
   • 无真实轨迹数据，无法评估预测精度
   • 建议提供真实数据进行完整的误差分析
            """
        
        return report


def plot_error_evolution(trajectories: Float[Array, "N T 3"],
                        true_trajectories: Optional[Float[Array, "M T 3"]] = None,
                        time_points: Optional[Float[Array, "T"]] = None,
                        title: str = "误差演化",
                        output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制误差演化图的便捷函数
    
    Args:
        trajectories: 预测轨迹 [N, T, 3]
        true_trajectories: 真实轨迹 [M, T, 3] (可选)
        time_points: 时间点 [T]
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    visualizer = ErrorAnalysisVisualizer()
    return visualizer.plot_comprehensive_error_analysis(
        trajectories, true_trajectories, time_points, title.replace(" ", "_"))


def plot_convergence_analysis(trajectories: Float[Array, "N T 3"],
                            time_points: Optional[Float[Array, "T"]] = None,
                            title: str = "收敛性分析",
                            output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制收敛性分析图的便捷函数
    
    Args:
        trajectories: 轨迹数据 [N, T, 3]
        time_points: 时间点 [T]
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    visualizer = ErrorAnalysisVisualizer()
    
    if time_points is None:
        time_points = jnp.linspace(0, 10, trajectories.shape[1])
    
    plt.figure(figsize=(10, 6))
    ax = plt.gca()
    
    visualizer._plot_convergence_analysis(ax, trajectories, time_points)
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.show()
        return None


def plot_manifold_consistency(trajectories: Float[Array, "N T 3"],
                            time_points: Optional[Float[Array, "T"]] = None,
                            title: str = "流形一致性检查",
                            output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制流形一致性检查图的便捷函数
    
    Args:
        trajectories: 轨迹数据 [N, T, 3]
        time_points: 时间点 [T]
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    visualizer = ErrorAnalysisVisualizer()
    
    if time_points is None:
        time_points = jnp.linspace(0, 10, trajectories.shape[1])
    
    plt.figure(figsize=(10, 6))
    ax = plt.gca()
    
    cos_theta, sin_theta = trajectories[..., 0], trajectories[..., 1]
    visualizer._plot_manifold_constraint_error(ax, cos_theta, sin_theta, time_points)
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.show()
        return None