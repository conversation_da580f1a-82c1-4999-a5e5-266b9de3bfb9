"""
交互式仪表板模块

提供交互式可视化仪表板功能：
- 实时指标监控
- 参数敏感性分析
- 交互式图表
- 动态结果展示
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.widgets import <PERSON>lider, But<PERSON>, CheckButtons
from matplotlib.animation import FuncAnimation
import numpy as np
import jax.numpy as jnp
from jaxtyping import Float, Array
from typing import Optional, Tuple, List, Dict, Any, Callable
import seaborn as sns
from pathlib import Path
import datetime
import time

# 设置绘图样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-darkgrid')


class PendulumDashboard:
    """大单摆交互式仪表板"""
    
    def __init__(self, 
                 trajectories: Float[Array, "N T 3"],
                 time_points: Optional[Float[Array, "T"]] = None,
                 true_trajectories: Optional[Float[Array, "M T 3"]] = None,
                 metrics: Optional[Dict[str, float]] = None):
        """
        初始化交互式仪表板
        
        Args:
            trajectories: 预测轨迹 [N, T, 3]
            time_points: 时间点 [T]
            true_trajectories: 真实轨迹 [M, T, 3] (可选)
            metrics: 评估指标字典 (可选)
        """
        self.trajectories = trajectories
        self.true_trajectories = true_trajectories
        self.metrics = metrics or {}
        
        N, T, D = trajectories.shape
        self.N, self.T, self.D = N, T, D
        
        if time_points is None:
            self.time_points = jnp.linspace(0, 10, T)
        else:
            self.time_points = time_points
        
        # 提取数据
        cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
        self.theta = jnp.arctan2(sin_theta, cos_theta)
        self.omega = omega
        
        # 当前显示状态
        self.current_trajectory = 0
        self.show_true_data = True if true_trajectories is not None else False
        self.animation_speed = 50  # ms
        self.is_playing = False
        
        # 颜色配置
        self.colors = sns.color_palette("husl", 8)
        
    def create_dashboard(self, output_path: Optional[str] = None) -> str:
        """
        创建交互式仪表板
        
        Args:
            output_path: 输出路径（可选）
            
        Returns:
            保存路径
        """
        # 创建主窗口
        self.fig = plt.figure(figsize=(20, 14))
        self.fig.suptitle('大单摆实验交互式仪表板', fontsize=16, fontweight='bold')
        
        # 创建网格布局
        gs = self.fig.add_gridspec(4, 4, hspace=0.4, wspace=0.3,
                                  left=0.05, right=0.95, top=0.92, bottom=0.08)
        
        # 1. 主要轨迹图 (左上，2x2)
        self.ax_main = self.fig.add_subplot(gs[0:2, 0:2])
        self.setup_main_trajectory_plot()
        
        # 2. 相位空间图 (右上)
        self.ax_phase = self.fig.add_subplot(gs[0, 2])
        self.setup_phase_space_plot()
        
        # 3. 能量分析 (右上下)
        self.ax_energy = self.fig.add_subplot(gs[0, 3])
        self.setup_energy_plot()
        
        # 4. 实时指标 (右中)
        self.ax_metrics = self.fig.add_subplot(gs[1, 2])
        self.setup_metrics_display()
        
        # 5. 误差分析 (右中下)
        self.ax_error = self.fig.add_subplot(gs[1, 3])
        self.setup_error_analysis()
        
        # 6. 分布图 (左下)
        self.ax_dist = self.fig.add_subplot(gs[2, 0])
        self.setup_distribution_plot()
        
        # 7. 统计图 (左下右)
        self.ax_stats = self.fig.add_subplot(gs[2, 1])
        self.setup_statistics_plot()
        
        # 8. 3D可视化 (右下，2x1)
        self.ax_3d = self.fig.add_subplot(gs[2, 2:4], projection='3d')
        self.setup_3d_visualization()
        
        # 9. 控制面板 (底部)
        self.setup_control_panel(gs[3, :])
        
        # 初始化所有图表
        self.update_all_plots()
        
        # 保存或显示
        if output_path:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            if not output_path.endswith('.png'):
                output_path = f"{output_path}_dashboard_{timestamp}.png"
            
            plt.savefig(output_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()
            print(f"✅ 交互式仪表板已保存至: {output_path}")
            return output_path
        else:
            plt.show()
            return "interactive_display"
    
    def setup_main_trajectory_plot(self):
        """设置主轨迹图"""
        self.ax_main.set_xlabel('时间 (s)')
        self.ax_main.set_ylabel('角度 θ (rad)')
        self.ax_main.set_title('轨迹时间序列')
        self.ax_main.grid(True, alpha=0.3)
        
        # 预创建线条对象
        self.trajectory_lines = []
        for i in range(min(self.N, 10)):
            line, = self.ax_main.plot([], [], alpha=0.7, linewidth=1.5, color=self.colors[i % len(self.colors)])
            self.trajectory_lines.append(line)
        
        # 当前轨迹高亮
        self.current_line, = self.ax_main.plot([], [], linewidth=3, color='red', label='当前轨迹')
        
        # 真实轨迹（如果有）
        if self.true_trajectories is not None:
            self.true_line, = self.ax_main.plot([], [], 'k--', linewidth=2, alpha=0.8, label='真实轨迹')
        
        self.ax_main.legend()
    
    def setup_phase_space_plot(self):
        """设置相位空间图"""
        self.ax_phase.set_xlabel('角度 θ (rad)')
        self.ax_phase.set_ylabel('角速度 ω (rad/s)')
        self.ax_phase.set_title('相位空间')
        self.ax_phase.grid(True, alpha=0.3)
        
        # 预创建相位轨迹
        self.phase_lines = []
        for i in range(min(self.N, 5)):
            line, = self.ax_phase.plot([], [], alpha=0.6, linewidth=1.5)
            self.phase_lines.append(line)
        
        # 当前相位轨迹
        self.current_phase_line, = self.ax_phase.plot([], [], linewidth=3, color='red')
        
        # 起始和结束点
        self.phase_start_point = self.ax_phase.scatter([], [], s=80, c='green', marker='o', zorder=5)
        self.phase_end_point = self.ax_phase.scatter([], [], s=80, c='red', marker='s', zorder=5)
    
    def setup_energy_plot(self):
        """设置能量分析图"""
        self.ax_energy.set_xlabel('时间 (s)')
        self.ax_energy.set_ylabel('能量')
        self.ax_energy.set_title('能量分析')
        self.ax_energy.grid(True, alpha=0.3)
        
        # 能量线条
        self.energy_total_line, = self.ax_energy.plot([], [], label='总能量', linewidth=2)
        self.energy_kinetic_line, = self.ax_energy.plot([], [], ':', label='动能', alpha=0.7)
        self.energy_potential_line, = self.ax_energy.plot([], [], '--', label='势能', alpha=0.7)
        
        self.ax_energy.legend()
    
    def setup_metrics_display(self):
        """设置指标显示"""
        self.ax_metrics.axis('off')
        self.ax_metrics.set_title('实时指标')
        
        # 指标文本
        self.metrics_text = self.ax_metrics.text(0.05, 0.95, '', transform=self.ax_metrics.transAxes,
                                               fontsize=10, verticalalignment='top', fontfamily='monospace',
                                               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    def setup_error_analysis(self):
        """设置误差分析"""
        self.ax_error.set_xlabel('时间 (s)')
        self.ax_error.set_ylabel('误差')
        self.ax_error.set_title('误差分析')
        self.ax_error.grid(True, alpha=0.3)
        
        # 误差线条
        self.error_manifold_line, = self.ax_error.semilogy([], [], label='流形约束误差', linewidth=2)
        self.error_energy_line, = self.ax_error.semilogy([], [], label='能量守恒误差', linewidth=2)
        
        self.ax_error.legend()
    
    def setup_distribution_plot(self):
        """设置分布图"""
        self.ax_dist.set_xlabel('数值')
        self.ax_dist.set_ylabel('概率密度')
        self.ax_dist.set_title('当前分布')
        self.ax_dist.grid(True, alpha=0.3)
    
    def setup_statistics_plot(self):
        """设置统计图"""
        self.ax_stats.axis('off')
        self.ax_stats.set_title('统计信息')
        
        # 统计文本
        self.stats_text = self.ax_stats.text(0.05, 0.95, '', transform=self.ax_stats.transAxes,
                                           fontsize=9, verticalalignment='top', fontfamily='monospace',
                                           bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    def setup_3d_visualization(self):
        """设置3D可视化"""
        self.ax_3d.set_xlabel('cos θ')
        self.ax_3d.set_ylabel('sin θ') 
        self.ax_3d.set_zlabel('ω (rad/s)')
        self.ax_3d.set_title('3D轨迹可视化')
        
        # 3D轨迹线
        self.trajectory_3d_lines = []
        for i in range(min(self.N, 5)):
            line, = self.ax_3d.plot([], [], [], alpha=0.6, linewidth=1.5)
            self.trajectory_3d_lines.append(line)
        
        # 当前3D轨迹
        self.current_3d_line, = self.ax_3d.plot([], [], [], linewidth=3, color='red')
        
        # 单位圆柱面（流形约束可视化）
        u = np.linspace(0, 2 * np.pi, 50)
        z_cyl = np.linspace(-3, 3, 20)
        U, Z = np.meshgrid(u, z_cyl)
        X_cyl = np.cos(U)
        Y_cyl = np.sin(U)
        self.ax_3d.plot_surface(X_cyl, Y_cyl, Z, alpha=0.1, color='gray')
    
    def setup_control_panel(self, gs_bottom):
        """设置控制面板"""
        # 创建控制面板区域
        control_fig = self.fig.add_subplot(gs_bottom)
        control_fig.axis('off')
        
        # 轨迹选择滑块
        ax_slider = plt.axes([0.1, 0.02, 0.3, 0.03])
        self.trajectory_slider = Slider(ax_slider, '轨迹选择', 0, min(self.N-1, 9), 
                                      valinit=0, valfmt='%d', valstep=1)
        self.trajectory_slider.on_changed(self.update_trajectory_selection)
        
        # 播放/暂停按钮
        ax_play = plt.axes([0.45, 0.02, 0.08, 0.03])
        self.play_button = Button(ax_play, '播放')
        self.play_button.on_clicked(self.toggle_animation)
        
        # 重置按钮
        ax_reset = plt.axes([0.55, 0.02, 0.08, 0.03])
        self.reset_button = Button(ax_reset, '重置')
        self.reset_button.on_clicked(self.reset_view)
        
        # 显示选项复选框
        if self.true_trajectories is not None:
            ax_check = plt.axes([0.7, 0.01, 0.15, 0.05])
            self.display_options = CheckButtons(ax_check, ['显示真实轨迹'], [self.show_true_data])
            self.display_options.on_clicked(self.toggle_display_options)
    
    def update_all_plots(self):
        """更新所有图表"""
        self.update_main_trajectory()
        self.update_phase_space()
        self.update_energy_analysis()
        self.update_metrics_display()
        self.update_error_analysis()
        self.update_distribution()
        self.update_statistics()
        self.update_3d_visualization()
        
        # 刷新图表
        self.fig.canvas.draw_idle()
    
    def update_main_trajectory(self):
        """更新主轨迹图"""
        # 更新所有轨迹
        for i, line in enumerate(self.trajectory_lines):
            if i < self.N:
                line.set_data(self.time_points, self.theta[i])
            else:
                line.set_data([], [])
        
        # 更新当前轨迹高亮
        current_idx = self.current_trajectory
        self.current_line.set_data(self.time_points, self.theta[current_idx])
        
        # 更新真实轨迹
        if self.true_trajectories is not None and self.show_true_data:
            true_theta = jnp.arctan2(self.true_trajectories[current_idx, :, 1], 
                                   self.true_trajectories[current_idx, :, 0])
            self.true_line.set_data(self.time_points, true_theta)
            self.true_line.set_visible(True)
        elif hasattr(self, 'true_line'):
            self.true_line.set_visible(False)
        
        # 自动调整坐标轴
        self.ax_main.relim()
        self.ax_main.autoscale_view()
    
    def update_phase_space(self):
        """更新相位空间图"""
        # 更新相位轨迹
        for i, line in enumerate(self.phase_lines):
            if i < min(self.N, 5):
                line.set_data(self.theta[i], self.omega[i])
            else:
                line.set_data([], [])
        
        # 更新当前相位轨迹
        current_idx = self.current_trajectory
        self.current_phase_line.set_data(self.theta[current_idx], self.omega[current_idx])
        
        # 更新起始和结束点
        self.phase_start_point.set_offsets([[self.theta[current_idx, 0], self.omega[current_idx, 0]]])
        self.phase_end_point.set_offsets([[self.theta[current_idx, -1], self.omega[current_idx, -1]]])
        
        # 自动调整坐标轴
        self.ax_phase.relim()
        self.ax_phase.autoscale_view()
    
    def update_energy_analysis(self):
        """更新能量分析"""
        current_idx = self.current_trajectory
        
        cos_theta = self.trajectories[current_idx, :, 0]
        sin_theta = self.trajectories[current_idx, :, 1]
        omega = self.trajectories[current_idx, :, 2]
        
        # 计算能量
        kinetic_energy = 0.5 * omega**2
        potential_energy = -cos_theta  # g/L = 1
        total_energy = kinetic_energy + potential_energy
        
        # 更新能量线条
        self.energy_total_line.set_data(self.time_points, total_energy)
        self.energy_kinetic_line.set_data(self.time_points, kinetic_energy)
        self.energy_potential_line.set_data(self.time_points, potential_energy)
        
        # 自动调整坐标轴
        self.ax_energy.relim()
        self.ax_energy.autoscale_view()
    
    def update_metrics_display(self):
        """更新指标显示"""
        current_idx = self.current_trajectory
        
        # 计算当前轨迹的关键指标
        cos_theta = self.trajectories[current_idx, :, 0]
        sin_theta = self.trajectories[current_idx, :, 1]
        omega = self.trajectories[current_idx, :, 2]
        
        # 流形约束误差
        manifold_error = float(jnp.mean(jnp.abs(cos_theta**2 + sin_theta**2 - 1.0)))
        
        # 能量守恒误差
        kinetic_energy = 0.5 * omega**2
        potential_energy = -cos_theta
        total_energy = kinetic_energy + potential_energy
        energy_conservation_error = float(jnp.var(total_energy))
        
        # 角度统计
        theta = jnp.arctan2(sin_theta, cos_theta)
        theta_mean = float(jnp.mean(theta))
        theta_std = float(jnp.std(theta))
        omega_mean = float(jnp.mean(omega))
        omega_std = float(jnp.std(omega))
        
        metrics_text = f"""
当前轨迹 #{current_idx+1}:
{'='*25}

角度统计:
  均值: {theta_mean:.3f} rad
  标准差: {theta_std:.3f} rad

角速度统计:
  均值: {omega_mean:.3f} rad/s
  标准差: {omega_std:.3f} rad/s

物理约束:
  流形误差: {manifold_error:.2e}
  能量误差: {energy_conservation_error:.2e}
        """
        
        # 添加全局指标（如果有）
        if self.metrics:
            metrics_text += f"""
全局指标:
  NLL: {self.metrics.get('nll', 'N/A')}
  Coverage: {self.metrics.get('coverage_95', 'N/A')}
  Bimodality: {self.metrics.get('bimodality_score', 'N/A')}
            """
        
        self.metrics_text.set_text(metrics_text)
    
    def update_error_analysis(self):
        """更新误差分析"""
        # 计算所有轨迹的误差指标
        cos_theta, sin_theta, omega = self.trajectories[..., 0], self.trajectories[..., 1], self.trajectories[..., 2]
        
        # 流形约束误差
        manifold_constraint = cos_theta**2 + sin_theta**2
        manifold_error = jnp.abs(manifold_constraint - 1.0)
        mean_manifold_error = jnp.mean(manifold_error, axis=0)
        
        # 能量守恒误差
        kinetic_energy = 0.5 * omega**2
        potential_energy = -cos_theta
        total_energy = kinetic_energy + potential_energy
        
        energy_errors = []
        for i in range(self.N):
            initial_energy = total_energy[i, 0]
            energy_error = jnp.abs(total_energy[i] - initial_energy)
            energy_errors.append(energy_error)
        
        mean_energy_error = jnp.mean(jnp.array(energy_errors), axis=0)
        
        # 更新误差线条
        self.error_manifold_line.set_data(self.time_points, mean_manifold_error)
        self.error_energy_line.set_data(self.time_points, mean_energy_error)
        
        # 自动调整坐标轴
        self.ax_error.relim()
        self.ax_error.autoscale_view()
    
    def update_distribution(self):
        """更新分布图"""
        self.ax_dist.clear()
        
        current_idx = self.current_trajectory
        theta = jnp.arctan2(self.trajectories[current_idx, :, 1], self.trajectories[current_idx, :, 0])
        
        # 绘制当前轨迹的角度分布
        self.ax_dist.hist(theta, bins=30, alpha=0.7, density=True, color=self.colors[current_idx % len(self.colors)])
        
        # 如果有真实数据且选择显示
        if self.true_trajectories is not None and self.show_true_data:
            true_theta = jnp.arctan2(self.true_trajectories[current_idx, :, 1], 
                                   self.true_trajectories[current_idx, :, 0])
            self.ax_dist.hist(true_theta, bins=30, alpha=0.5, density=True, color='red')
        
        self.ax_dist.set_xlabel('角度 θ (rad)')
        self.ax_dist.set_ylabel('概率密度')
        self.ax_dist.set_title(f'轨迹 #{current_idx+1} 角度分布')
        self.ax_dist.grid(True, alpha=0.3)
    
    def update_statistics(self):
        """更新统计信息"""
        # 计算整体统计
        all_theta = jnp.arctan2(self.trajectories[..., 1], self.trajectories[..., 0])
        all_omega = self.trajectories[..., 2]
        
        theta_global_mean = float(jnp.mean(all_theta))
        theta_global_std = float(jnp.std(all_theta))
        omega_global_mean = float(jnp.mean(all_omega))
        omega_global_std = float(jnp.std(all_omega))
        
        # 流形和能量统计
        cos_theta, sin_theta = self.trajectories[..., 0], self.trajectories[..., 1]
        manifold_error = float(jnp.mean(jnp.abs(cos_theta**2 + sin_theta**2 - 1.0)))
        
        stats_text = f"""
全局统计 (N={self.N}):
{'='*20}

角度:
  均值: {theta_global_mean:.3f} rad
  标准差: {theta_global_std:.3f} rad
  范围: [{float(jnp.min(all_theta)):.2f}, 
         {float(jnp.max(all_theta)):.2f}]

角速度:
  均值: {omega_global_mean:.3f} rad/s
  标准差: {omega_global_std:.3f} rad/s
  范围: [{float(jnp.min(all_omega)):.2f},
         {float(jnp.max(all_omega)):.2f}]

约束:
  流形误差: {manifold_error:.2e}
  约束满足: {(1-manifold_error)*100:.3f}%
        """
        
        self.stats_text.set_text(stats_text)
    
    def update_3d_visualization(self):
        """更新3D可视化"""
        # 清除旧的轨迹数据
        for line in self.trajectory_3d_lines:
            line.set_data_3d([], [], [])
        
        # 更新3D轨迹
        for i, line in enumerate(self.trajectory_3d_lines):
            if i < min(self.N, 5):
                cos_theta = self.trajectories[i, :, 0]
                sin_theta = self.trajectories[i, :, 1]
                omega = self.trajectories[i, :, 2]
                line.set_data_3d(cos_theta, sin_theta, omega)
        
        # 更新当前3D轨迹
        current_idx = self.current_trajectory
        cos_theta = self.trajectories[current_idx, :, 0]
        sin_theta = self.trajectories[current_idx, :, 1]
        omega = self.trajectories[current_idx, :, 2]
        self.current_3d_line.set_data_3d(cos_theta, sin_theta, omega)
        
        # 自动调整视角
        self.ax_3d.set_xlim([-1.5, 1.5])
        self.ax_3d.set_ylim([-1.5, 1.5])
        omega_range = [float(jnp.min(self.trajectories[..., 2])), float(jnp.max(self.trajectories[..., 2]))]
        self.ax_3d.set_zlim(omega_range)
    
    def update_trajectory_selection(self, val):
        """更新轨迹选择"""
        self.current_trajectory = int(self.trajectory_slider.val)
        self.update_all_plots()
    
    def toggle_animation(self, event):
        """切换动画播放状态"""
        self.is_playing = not self.is_playing
        if self.is_playing:
            self.play_button.label.set_text('暂停')
            self.start_animation()
        else:
            self.play_button.label.set_text('播放')
            self.stop_animation()
    
    def start_animation(self):
        """开始动画"""
        if hasattr(self, 'animation'):
            self.animation.event_source.stop()
        
        self.animation = FuncAnimation(self.fig, self.animate_frame, 
                                     frames=min(self.N, 10), 
                                     interval=self.animation_speed, 
                                     repeat=True)
    
    def stop_animation(self):
        """停止动画"""
        if hasattr(self, 'animation'):
            self.animation.event_source.stop()
    
    def animate_frame(self, frame):
        """动画帧更新"""
        if self.is_playing:
            self.trajectory_slider.set_val(frame)
            return []
    
    def reset_view(self, event):
        """重置视图"""
        self.current_trajectory = 0
        self.trajectory_slider.set_val(0)
        self.is_playing = False
        self.play_button.label.set_text('播放')
        if hasattr(self, 'animation'):
            self.animation.event_source.stop()
        self.update_all_plots()
    
    def toggle_display_options(self, label):
        """切换显示选项"""
        if label == '显示真实轨迹':
            self.show_true_data = not self.show_true_data
            self.update_all_plots()


def create_interactive_dashboard(trajectories: Float[Array, "N T 3"],
                               time_points: Optional[Float[Array, "T"]] = None,
                               true_trajectories: Optional[Float[Array, "M T 3"]] = None,
                               metrics: Optional[Dict[str, float]] = None,
                               output_path: Optional[str] = None) -> str:
    """
    创建交互式仪表板的便捷函数
    
    Args:
        trajectories: 预测轨迹 [N, T, 3]
        time_points: 时间点 [T]
        true_trajectories: 真实轨迹 [M, T, 3] (可选)
        metrics: 评估指标字典 (可选)
        output_path: 输出路径（可选）
        
    Returns:
        保存路径或显示状态
    """
    dashboard = PendulumDashboard(trajectories, time_points, true_trajectories, metrics)
    return dashboard.create_dashboard(output_path)