"""
大单摆轨迹可视化模块

提供专业的单摆轨迹可视化功能：
- 时间序列轨迹
- 相位空间图
- 能量分析图
- 角度轨迹图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import numpy as np
import jax.numpy as jnp
from jaxtyping import Float, Array
from typing import Optional, Tuple, List, Dict, Any
import seaborn as sns
from pathlib import Path
import datetime

# 设置中文字体和科学绘图样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")


class PendulumTrajectoryVisualizer:
    """大单摆轨迹可视化器"""
    
    def __init__(self, output_dir: str = "results/visualization"):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置绘图参数
        self.figure_size = (15, 10)
        self.dpi = 300
        self.color_palette = sns.color_palette("husl", 8)
        
    def plot_complete_analysis(self,
                             trajectories: Float[Array, "N T 3"],
                             true_trajectories: Optional[Float[Array, "M T 3"]] = None,
                             time_points: Optional[Float[Array, "T"]] = None,
                             experiment_name: str = "pendulum_analysis",
                             g_over_L: float = 1.0) -> str:
        """
        绘制完整的单摆分析图
        
        Args:
            trajectories: 预测轨迹 [N, T, 3] (cos_theta, sin_theta, omega)
            true_trajectories: 真实轨迹 [M, T, 3] (可选)
            time_points: 时间点 [T]
            experiment_name: 实验名称
            g_over_L: 重力加速度与长度比值
            
        Returns:
            保存的文件路径
        """
        N, T, D = trajectories.shape
        
        if time_points is None:
            time_points = jnp.linspace(0, 10, T)
        
        # 创建大图
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 3, hspace=0.3, wspace=0.3)
        
        # 提取角度和角速度
        cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        # 1. 角度时间序列 (左上)
        ax1 = fig.add_subplot(gs[0, 0])
        for i in range(min(N, 10)):  # 最多显示10条轨迹
            ax1.plot(time_points, theta[i], alpha=0.7, linewidth=1.5)
        if true_trajectories is not None:
            true_theta = jnp.arctan2(true_trajectories[..., 1], true_trajectories[..., 0])
            for i in range(min(true_trajectories.shape[0], 5)):
                ax1.plot(time_points, true_theta[i], 'k--', alpha=0.8, linewidth=2, label='真实轨迹' if i == 0 else '')
        ax1.set_xlabel('时间 (s)')
        ax1.set_ylabel('角度 θ (rad)')
        ax1.set_title('角度时间序列')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 角速度时间序列 (中上)
        ax2 = fig.add_subplot(gs[0, 1])
        for i in range(min(N, 10)):
            ax2.plot(time_points, omega[i], alpha=0.7, linewidth=1.5)
        if true_trajectories is not None:
            for i in range(min(true_trajectories.shape[0], 5)):
                ax2.plot(time_points, true_trajectories[i, :, 2], 'k--', alpha=0.8, linewidth=2)
        ax2.set_xlabel('时间 (s)')
        ax2.set_ylabel('角速度 ω (rad/s)')
        ax2.set_title('角速度时间序列')
        ax2.grid(True, alpha=0.3)
        
        # 3. 相位空间图 (右上)
        ax3 = fig.add_subplot(gs[0, 2])
        for i in range(min(N, 10)):
            ax3.plot(theta[i], omega[i], alpha=0.6, linewidth=1.5)
            ax3.scatter(theta[i, 0], omega[i, 0], s=50, c='green', marker='o', alpha=0.8, zorder=5)
            ax3.scatter(theta[i, -1], omega[i, -1], s=50, c='red', marker='s', alpha=0.8, zorder=5)
        if true_trajectories is not None:
            true_theta = jnp.arctan2(true_trajectories[..., 1], true_trajectories[..., 0])
            for i in range(min(true_trajectories.shape[0], 5)):
                ax3.plot(true_theta[i], true_trajectories[i, :, 2], 'k--', alpha=0.8, linewidth=2)
        ax3.set_xlabel('角度 θ (rad)')
        ax3.set_ylabel('角速度 ω (rad/s)')
        ax3.set_title('相位空间图')
        ax3.grid(True, alpha=0.3)
        
        # 4. 能量分析 (左中)
        ax4 = fig.add_subplot(gs[1, 0])
        kinetic_energy = 0.5 * omega**2
        potential_energy = -g_over_L * cos_theta
        total_energy = kinetic_energy + potential_energy
        
        for i in range(min(N, 10)):
            ax4.plot(time_points, total_energy[i], alpha=0.7, linewidth=1.5, label='总能量' if i == 0 else '')
            if i < 3:  # 只显示前3条的动能和势能
                ax4.plot(time_points, kinetic_energy[i], ':', alpha=0.5, linewidth=1)
                ax4.plot(time_points, potential_energy[i], '--', alpha=0.5, linewidth=1)
        ax4.set_xlabel('时间 (s)')
        ax4.set_ylabel('能量')
        ax4.set_title('能量守恒分析')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 5. 流形约束检查 (中中)
        ax5 = fig.add_subplot(gs[1, 1])
        manifold_constraint = cos_theta**2 + sin_theta**2
        for i in range(min(N, 10)):
            ax5.plot(time_points, manifold_constraint[i], alpha=0.7, linewidth=1.5)
        ax5.axhline(y=1.0, color='red', linestyle='--', linewidth=2, label='理想约束 (= 1)')
        ax5.set_xlabel('时间 (s)')
        ax5.set_ylabel('cos²θ + sin²θ')
        ax5.set_title('流形约束检查')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. 角度分布直方图 (右中)
        ax6 = fig.add_subplot(gs[1, 2])
        all_theta = theta.flatten()
        ax6.hist(all_theta, bins=50, alpha=0.7, density=True, color=self.color_palette[0])
        if true_trajectories is not None:
            true_theta_flat = jnp.arctan2(true_trajectories[..., 1], true_trajectories[..., 0]).flatten()
            ax6.hist(true_theta_flat, bins=50, alpha=0.5, density=True, color='black', label='真实分布')
        ax6.set_xlabel('角度 θ (rad)')
        ax6.set_ylabel('概率密度')
        ax6.set_title('角度分布')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        
        # 7. 角速度分布直方图 (左下)
        ax7 = fig.add_subplot(gs[2, 0])
        all_omega = omega.flatten()
        ax7.hist(all_omega, bins=50, alpha=0.7, density=True, color=self.color_palette[1])
        if true_trajectories is not None:
            true_omega_flat = true_trajectories[..., 2].flatten()
            ax7.hist(true_omega_flat, bins=50, alpha=0.5, density=True, color='black', label='真实分布')
        ax7.set_xlabel('角速度 ω (rad/s)')
        ax7.set_ylabel('概率密度')
        ax7.set_title('角速度分布')
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        
        # 8. 能量分布直方图 (中下)
        ax8 = fig.add_subplot(gs[2, 1])
        all_energy = total_energy.flatten()
        ax8.hist(all_energy, bins=50, alpha=0.7, density=True, color=self.color_palette[2])
        ax8.set_xlabel('总能量')
        ax8.set_ylabel('概率密度')
        ax8.set_title('能量分布')
        ax8.grid(True, alpha=0.3)
        
        # 9. 庞加莱截面 (右下)
        ax9 = fig.add_subplot(gs[2, 2])
        # 找到θ=0的交叉点（向上穿越）
        poincare_theta = []
        poincare_omega = []
        for i in range(min(N, 20)):
            theta_traj = theta[i]
            omega_traj = omega[i]
            # 找到向上穿越θ=0的点
            for t in range(len(theta_traj)-1):
                if theta_traj[t] < 0 and theta_traj[t+1] >= 0 and omega_traj[t] > 0:
                    # 线性插值找到精确交叉点
                    alpha = -theta_traj[t] / (theta_traj[t+1] - theta_traj[t])
                    omega_cross = omega_traj[t] + alpha * (omega_traj[t+1] - omega_traj[t])
                    poincare_omega.append(omega_cross)
        
        if poincare_omega:
            ax9.scatter(range(len(poincare_omega)), poincare_omega, alpha=0.7, s=30)
            ax9.set_xlabel('穿越次数')
            ax9.set_ylabel('角速度 ω (rad/s)')
            ax9.set_title('庞加莱截面 (θ=0, ω>0)')
        else:
            ax9.text(0.5, 0.5, '无足够数据\n生成庞加莱截面', ha='center', va='center', transform=ax9.transAxes)
            ax9.set_title('庞加莱截面')
        ax9.grid(True, alpha=0.3)
        
        # 10. 统计信息表 (底部跨列)
        ax10 = fig.add_subplot(gs[3, :])
        ax10.axis('off')
        
        # 计算统计信息
        stats_text = self._compute_trajectory_statistics(trajectories, true_trajectories, g_over_L)
        ax10.text(0.05, 0.95, stats_text, transform=ax10.transAxes, fontsize=12,
                 verticalalignment='top', fontfamily='monospace',
                 bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        # 添加总标题
        fig.suptitle(f'大单摆轨迹完整分析 - {experiment_name}', fontsize=16, fontweight='bold')
        
        # 保存图片
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{experiment_name}_complete_analysis_{timestamp}.png"
        filepath = self.output_dir / filename
        
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 完整分析图已保存至: {filepath}")
        return str(filepath)
    
    def _compute_trajectory_statistics(self, 
                                     trajectories: Float[Array, "N T 3"],
                                     true_trajectories: Optional[Float[Array, "M T 3"]],
                                     g_over_L: float) -> str:
        """计算轨迹统计信息"""
        N, T, D = trajectories.shape
        
        # 基础统计
        cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
        theta = jnp.arctan2(sin_theta, cos_theta)
        
        # 能量统计
        kinetic_energy = 0.5 * omega**2
        potential_energy = -g_over_L * cos_theta
        total_energy = kinetic_energy + potential_energy
        
        energy_mean = float(jnp.mean(total_energy))
        energy_std = float(jnp.std(total_energy))
        energy_conservation_error = float(jnp.mean(jnp.var(total_energy, axis=1)))
        
        # 流形约束
        manifold_constraint = cos_theta**2 + sin_theta**2
        manifold_error = float(jnp.mean(jnp.abs(manifold_constraint - 1.0)))
        
        # 角度统计
        theta_mean = float(jnp.mean(theta))
        theta_std = float(jnp.std(theta))
        omega_mean = float(jnp.mean(omega))
        omega_std = float(jnp.std(omega))
        
        stats_text = f"""
轨迹统计信息 (N={N}, T={T}):
════════════════════════════════════════
能量分析:
  • 平均能量: {energy_mean:.4f}
  • 能量标准差: {energy_std:.4f}  
  • 能量守恒误差: {energy_conservation_error:.6f}

角度分析:
  • 平均角度: {theta_mean:.4f} rad ({np.degrees(theta_mean):.1f}°)
  • 角度标准差: {theta_std:.4f} rad ({np.degrees(theta_std):.1f}°)
  • 平均角速度: {omega_mean:.4f} rad/s
  • 角速度标准差: {omega_std:.4f} rad/s

流形约束:
  • 流形约束误差: {manifold_error:.6f}
  • 约束满足度: {(1-manifold_error)*100:.2f}%
        """
        
        if true_trajectories is not None:
            # 与真实轨迹的比较
            true_theta = jnp.arctan2(true_trajectories[..., 1], true_trajectories[..., 0])
            true_omega = true_trajectories[..., 2]
            
            theta_mse = float(jnp.mean((theta - true_theta[:N])**2))
            omega_mse = float(jnp.mean((omega - true_omega[:N])**2))
            
            stats_text += f"""
与真实轨迹比较:
  • 角度MSE: {theta_mse:.6f}
  • 角速度MSE: {omega_mse:.6f}
            """
        
        return stats_text


def plot_phase_space(trajectories: Float[Array, "N T 3"],
                    title: str = "相位空间图",
                    output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制相位空间图
    
    Args:
        trajectories: 轨迹数据 [N, T, 3]
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
    theta = jnp.arctan2(sin_theta, cos_theta)
    
    plt.figure(figsize=(12, 8))
    
    N = min(trajectories.shape[0], 20)  # 最多显示20条轨迹
    colors = plt.cm.tab20(np.linspace(0, 1, N))
    
    for i in range(N):
        plt.plot(theta[i], omega[i], color=colors[i], alpha=0.7, linewidth=1.5)
        plt.scatter(theta[i, 0], omega[i, 0], color=colors[i], s=50, marker='o', alpha=0.8, zorder=5)
        plt.scatter(theta[i, -1], omega[i, -1], color=colors[i], s=50, marker='s', alpha=0.8, zorder=5)
    
    plt.xlabel('角度 θ (rad)')
    plt.ylabel('角速度 ω (rad/s)')
    plt.title(title)
    plt.grid(True, alpha=0.3)
    
    # 添加图例
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='gray', linestyle='None', markersize=8, label='起始点'),
        plt.Line2D([0], [0], marker='s', color='gray', linestyle='None', markersize=8, label='结束点')
    ]
    plt.legend(handles=legend_elements)
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.show()
        return None


def plot_energy_analysis(trajectories: Float[Array, "N T 3"],
                        time_points: Optional[Float[Array, "T"]] = None,
                        g_over_L: float = 1.0,
                        title: str = "能量分析",
                        output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制能量分析图
    
    Args:
        trajectories: 轨迹数据 [N, T, 3]
        time_points: 时间点 [T]
        g_over_L: 重力加速度与长度比值
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    N, T, D = trajectories.shape
    
    if time_points is None:
        time_points = jnp.linspace(0, 10, T)
    
    cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
    
    # 计算能量
    kinetic_energy = 0.5 * omega**2
    potential_energy = -g_over_L * cos_theta
    total_energy = kinetic_energy + potential_energy
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 总能量时间序列
    ax1 = axes[0, 0]
    for i in range(min(N, 10)):
        ax1.plot(time_points, total_energy[i], alpha=0.7, linewidth=1.5)
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('总能量')
    ax1.set_title('总能量时间序列')
    ax1.grid(True, alpha=0.3)
    
    # 2. 动能vs势能
    ax2 = axes[0, 1]
    for i in range(min(N, 10)):
        ax2.plot(time_points, kinetic_energy[i], alpha=0.7, linewidth=1.5, label='动能' if i == 0 else '')
        ax2.plot(time_points, potential_energy[i], alpha=0.7, linewidth=1.5, linestyle='--', label='势能' if i == 0 else '')
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('能量')
    ax2.set_title('动能与势能')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 能量守恒误差
    ax3 = axes[1, 0]
    energy_errors = []
    for i in range(N):
        initial_energy = total_energy[i, 0]
        error = jnp.abs(total_energy[i] - initial_energy)
        energy_errors.append(error)
        if i < 10:
            ax3.plot(time_points, error, alpha=0.7, linewidth=1.5)
    
    mean_error = jnp.mean(jnp.array(energy_errors), axis=0)
    ax3.plot(time_points, mean_error, 'k-', linewidth=3, label='平均误差')
    ax3.set_xlabel('时间 (s)')
    ax3.set_ylabel('能量守恒误差')
    ax3.set_title('能量守恒误差')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_yscale('log')
    
    # 4. 能量分布
    ax4 = axes[1, 1]
    all_energy = total_energy.flatten()
    ax4.hist(all_energy, bins=50, alpha=0.7, density=True)
    ax4.set_xlabel('总能量')
    ax4.set_ylabel('概率密度')
    ax4.set_title('能量分布')
    ax4.grid(True, alpha=0.3)
    
    plt.suptitle(title, fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.show()
        return None


def plot_angular_trajectories(trajectories: Float[Array, "N T 3"],
                            time_points: Optional[Float[Array, "T"]] = None,
                            title: str = "角度轨迹",
                            output_path: Optional[str] = None) -> Optional[str]:
    """
    绘制角度轨迹图
    
    Args:
        trajectories: 轨迹数据 [N, T, 3]
        time_points: 时间点 [T]
        title: 图标题
        output_path: 输出路径（可选）
        
    Returns:
        保存路径（如果指定了output_path）
    """
    N, T, D = trajectories.shape
    
    if time_points is None:
        time_points = jnp.linspace(0, 10, T)
    
    cos_theta, sin_theta, omega = trajectories[..., 0], trajectories[..., 1], trajectories[..., 2]
    theta = jnp.arctan2(sin_theta, cos_theta)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 角度时间序列
    ax1 = axes[0, 0]
    for i in range(min(N, 10)):
        ax1.plot(time_points, theta[i], alpha=0.7, linewidth=1.5)
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('角度 θ (rad)')
    ax1.set_title('角度时间序列')
    ax1.grid(True, alpha=0.3)
    
    # 2. 角速度时间序列
    ax2 = axes[0, 1]
    for i in range(min(N, 10)):
        ax2.plot(time_points, omega[i], alpha=0.7, linewidth=1.5)
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('角速度 ω (rad/s)')
    ax2.set_title('角速度时间序列')
    ax2.grid(True, alpha=0.3)
    
    # 3. 角度分布
    ax3 = axes[1, 0]
    all_theta = theta.flatten()
    ax3.hist(all_theta, bins=50, alpha=0.7, density=True)
    ax3.set_xlabel('角度 θ (rad)')
    ax3.set_ylabel('概率密度')
    ax3.set_title('角度分布')
    ax3.grid(True, alpha=0.3)
    
    # 4. 角速度分布
    ax4 = axes[1, 1]
    all_omega = omega.flatten()
    ax4.hist(all_omega, bins=50, alpha=0.7, density=True)
    ax4.set_xlabel('角速度 ω (rad/s)')
    ax4.set_ylabel('概率密度')
    ax4.set_title('角速度分布')
    ax4.grid(True, alpha=0.3)
    
    plt.suptitle(title, fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.show()
        return None