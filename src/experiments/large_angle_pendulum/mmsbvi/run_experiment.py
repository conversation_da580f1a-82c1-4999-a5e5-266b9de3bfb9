"""
大角度单摆MMSBVI实验主脚本 (Large-Angle Pendulum MMSBVI Experiment Main Script)

Usage:
```bash
# 使用默认标准配置
python run_experiment.py

# 使用高性能配置
python run_experiment.py --config high_performance

# 自定义参数
python run_experiment.py --batch_size 512 --learning_rate 5e-4

# 调试模式
python run_experiment.py --config debug --results_dir debug_results

# 从配置文件加载
python run_experiment.py --load_config my_config.json
```

"""

import os
import sys
import argparse
import pathlib
import traceback
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import json

root_dir = pathlib.Path(__file__).resolve().parents[4]  # 回到项目根目录
src_dir = root_dir / "src"
if str(src_dir) not in sys.path:
    sys.path.append(str(src_dir))

import jax
import jax.numpy as jnp
import jax.random as random

jax.config.update('jax_enable_x64', True)  # 启用64位精度用于理论工作

from .config import (
    create_standard_config,
    create_high_performance_config,
    create_debug_config,
    create_cpu_config,
    create_custom_config,
    validate_config,
    save_config,
    load_config,
    print_config_summary,
    PendulumExperimentConfig
)

from .experiment_orchestrator import PendulumExperimentOrchestrator

try:
    from mmsbvi.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)


# ============================================================================
# Environment Setup / 环境设置
# ============================================================================

def setup_jax_environment(use_gpu: bool = True, memory_fraction: float = 0.9) -> None:
    """
    Setup JAX environment for optimal performance
    设置JAX环境以获得最佳性能
    
    Args:
        use_gpu: 是否使用GPU（如果可用）
        memory_fraction: GPU内存使用比例
    """
    # GPU/CPU设备配置
    if use_gpu and jax.devices("gpu"):
        logger.info(f"🚀 使用GPU设备: {jax.devices('gpu')}")
        # 配置GPU内存使用
        if hasattr(jax.config, 'update'):
            jax.config.update('jax_memory_fraction', memory_fraction)
    else:
        logger.info(f"🖥️  使用CPU设备: {jax.devices('cpu')}")
    
    # 性能优化设置
    jax.config.update('jax_enable_x64', True)  # 理论工作需要64位精度
    
    logger.info(f"JAX版本: {jax.__version__}")
    logger.info(f"可用设备: {jax.devices()}")
    logger.info(f"64位精度: {'启用' if jax.config.jax_enable_x64 else '禁用'}")


def set_random_seeds(seed: int) -> jax.random.PRNGKey:
    """
    Set random seeds for reproducibility across all libraries
    设置随机种子以确保所有库的可复现性
    
    Args:
        seed: 随机种子
        
    Returns:
        JAX随机密钥
    """
    # Python随机数
    import random
    random.seed(seed)
    
    # NumPy随机数
    import numpy as np
    np.random.seed(seed)
    
    # JAX随机数
    jax_key = jax.random.PRNGKey(seed)
    
    logger.info(f"🎲 随机种子设置: {seed}")
    return jax_key


# ============================================================================
# Command Line Interface / 命令行接口
# ============================================================================

def create_argument_parser() -> argparse.ArgumentParser:
    """
    Create command line argument parser
    创建命令行参数解析器
    
    Returns:
        配置好的参数解析器
    """
    parser = argparse.ArgumentParser(
        description="大角度单摆MMSBVI实验 / Large-Angle Pendulum MMSBVI Experiment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例 / Usage Examples:
  python run_experiment.py                           # 使用默认标准配置
  python run_experiment.py --config high_performance # 使用高性能配置  
  python run_experiment.py --batch_size 512         # 自定义批处理大小
  python run_experiment.py --config debug           # 调试模式
  python run_experiment.py --load_config config.json # 从文件加载配置
        """
    )
    
    # ============ 预设配置选择 ============
    config_group = parser.add_argument_group('配置预设 / Configuration Presets')
    config_group.add_argument(
        '--config', '-c',
        type=str,
        choices=['standard', 'high_performance', 'debug', 'cpu'],
        default='standard',
        help='预设配置类型 / Preset configuration type (default: standard)'
    )
    
    config_group.add_argument(
        '--load_config',
        type=str,
        help='从JSON文件加载配置 / Load configuration from JSON file'
    )
    
    config_group.add_argument(
        '--save_config',
        type=str,
        help='保存配置到JSON文件 / Save configuration to JSON file'
    )
    
    # ============ 物理参数覆盖 ============
    physics_group = parser.add_argument_group('物理参数 / Physics Parameters')
    physics_group.add_argument('--gravity', type=float, help='重力加速度 g (m/s²)')
    physics_group.add_argument('--length', type=float, help='摆长 L (m)')
    physics_group.add_argument('--mass', type=float, help='质量 m (kg)')
    physics_group.add_argument('--damping', type=float, help='阻尼系数 γ (s⁻¹)')
    physics_group.add_argument('--physics_weight', type=float, help='物理漂移权重')
    physics_group.add_argument('--network_weight', type=float, help='神经网络修正权重')
    
    # ============ 网络参数覆盖 ============
    network_group = parser.add_argument_group('神经网络参数 / Neural Network Parameters')
    network_group.add_argument('--hidden_dims', type=int, nargs='+', help='隐藏层维度列表')
    network_group.add_argument('--n_layers', type=int, help='网络层数')
    network_group.add_argument('--activation', type=str, help='激活函数')
    network_group.add_argument('--dropout_rate', type=float, help='Dropout率')
    network_group.add_argument('--time_encoding_dim', type=int, help='时间编码维度')
    
    # ============ 训练参数覆盖 ============  
    training_group = parser.add_argument_group('训练参数 / Training Parameters')
    training_group.add_argument('--batch_size', type=int, help='批处理大小')
    training_group.add_argument('--learning_rate', type=float, help='学习率')
    training_group.add_argument('--num_epochs', type=int, help='训练轮数')
    training_group.add_argument('--warmup_epochs', type=int, help='预热轮数')
    training_group.add_argument('--main_epochs', type=int, help='主训练轮数')
    
    # ============ 性能优化参数 ============
    perf_group = parser.add_argument_group('性能优化 / Performance Optimization')
    perf_group.add_argument('--mixed_precision', action='store_true', help='启用混合精度训练')
    perf_group.add_argument('--no_mixed_precision', action='store_true', help='禁用混合精度训练')
    perf_group.add_argument('--profiling', action='store_true', help='启用JAX Profiler')
    perf_group.add_argument('--no_profiling', action='store_true', help='禁用JAX Profiler')
    perf_group.add_argument('--adaptive_batch', action='store_true', help='启用自适应批处理大小')
    
    # ============ 实验设置 ============
    exp_group = parser.add_argument_group('实验设置 / Experiment Settings')
    exp_group.add_argument('--results_dir', type=str, help='结果保存目录')
    exp_group.add_argument('--random_seed', type=int, default=42, help='随机种子 (default: 42)')
    exp_group.add_argument('--validation_freq', type=int, help='验证频率')
    
    # ============ 系统设置 ============
    sys_group = parser.add_argument_group('系统设置 / System Settings')
    sys_group.add_argument('--use_cpu', action='store_true', help='强制使用CPU（忽略GPU）')
    sys_group.add_argument('--memory_fraction', type=float, default=0.9, help='GPU内存使用比例 (default: 0.9)')
    sys_group.add_argument('--verbose', '-v', action='store_true', help='详细输出模式')
    sys_group.add_argument('--quiet', '-q', action='store_true', help='静默模式')
    
    return parser


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments with validation
    解析并验证命令行参数
    
    Returns:
        解析后的参数命名空间
    """
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 参数验证和冲突检查
    if args.mixed_precision and args.no_mixed_precision:
        parser.error("不能同时指定 --mixed_precision 和 --no_mixed_precision")
    
    if args.profiling and args.no_profiling:
        parser.error("不能同时指定 --profiling 和 --no_profiling")
    
    if args.verbose and args.quiet:
        parser.error("不能同时指定 --verbose 和 --quiet")
    
    if args.load_config and args.config != 'standard':
        logger.warning("同时指定了 --load_config 和 --config，将优先使用 --load_config")
    
    return args


# ============================================================================
# Configuration Management / 配置管理
# ============================================================================

def create_config_from_args(args: argparse.Namespace) -> PendulumExperimentConfig:
    """
    Create experiment configuration from command line arguments
    从命令行参数创建实验配置
    
    Args:
        args: 解析后的命令行参数
        
    Returns:
        实验配置对象
    """
    # 如果指定了配置文件，直接加载
    if args.load_config:
        logger.info(f"📂 从文件加载配置: {args.load_config}")
        config = load_config(args.load_config)
        
        # 应用命令行参数覆盖
        config = apply_cli_overrides(config, args)
        return config
    
    # 根据预设类型创建基础配置
    logger.info(f"🔧 创建 '{args.config}' 预设配置")
    
    if args.config == 'standard':
        config = create_standard_config(
            results_dir=args.results_dir,
            random_seed=args.random_seed
        )
    elif args.config == 'high_performance':
        config = create_high_performance_config(
            results_dir=args.results_dir,
            random_seed=args.random_seed
        )
    elif args.config == 'debug':
        config = create_debug_config(
            results_dir=args.results_dir,
            random_seed=args.random_seed
        )
    elif args.config == 'cpu':
        config = create_cpu_config(
            results_dir=args.results_dir,
            random_seed=args.random_seed
        )
    else:
        raise ValueError(f"未知的配置类型: {args.config}")
    
    # 应用命令行参数覆盖
    config = apply_cli_overrides(config, args)
    
    return config


def apply_cli_overrides(config: PendulumExperimentConfig, 
                       args: argparse.Namespace) -> PendulumExperimentConfig:
    """
    Apply command line argument overrides to configuration
    将命令行参数覆盖应用到配置中
    
    Args:
        config: 基础配置
        args: 命令行参数
        
    Returns:
        覆盖后的配置
    """
    overrides_applied = []
    
    # ============ 物理参数覆盖 ============
    physics_params = {}
    if args.gravity is not None:
        physics_params['gravity'] = args.gravity
        overrides_applied.append(f"gravity={args.gravity}")
    if args.length is not None:
        physics_params['length'] = args.length
        overrides_applied.append(f"length={args.length}")
    if args.mass is not None:
        physics_params['mass'] = args.mass
        overrides_applied.append(f"mass={args.mass}")
    if args.damping is not None:
        physics_params['damping'] = args.damping
        overrides_applied.append(f"damping={args.damping}")
    if args.physics_weight is not None:
        physics_params['physics_weight'] = args.physics_weight
        overrides_applied.append(f"physics_weight={args.physics_weight}")
    if args.network_weight is not None:
        physics_params['network_weight'] = args.network_weight
        overrides_applied.append(f"network_weight={args.network_weight}")
    
    # ============ 网络参数覆盖 ============
    network_params = {}
    if args.hidden_dims is not None:
        network_params['hidden_dims'] = tuple(args.hidden_dims)
        overrides_applied.append(f"hidden_dims={args.hidden_dims}")
    if args.n_layers is not None:
        network_params['n_layers'] = args.n_layers
        overrides_applied.append(f"n_layers={args.n_layers}")
    if args.activation is not None:
        network_params['activation'] = args.activation
        overrides_applied.append(f"activation={args.activation}")
    if args.dropout_rate is not None:
        network_params['dropout_rate'] = args.dropout_rate
        overrides_applied.append(f"dropout_rate={args.dropout_rate}")
    if args.time_encoding_dim is not None:
        network_params['time_encoding_dim'] = args.time_encoding_dim
        overrides_applied.append(f"time_encoding_dim={args.time_encoding_dim}")
    
    # ============ 控制参数覆盖 ============
    control_params = {}
    if args.batch_size is not None:
        control_params['batch_size'] = args.batch_size
        overrides_applied.append(f"batch_size={args.batch_size}")
    if args.learning_rate is not None:
        control_params['learning_rate'] = args.learning_rate
        overrides_applied.append(f"learning_rate={args.learning_rate}")
    if args.num_epochs is not None:
        control_params['num_epochs'] = args.num_epochs
        overrides_applied.append(f"num_epochs={args.num_epochs}")
    
    # ============ 实验参数覆盖 ============
    experiment_params = {}
    if args.warmup_epochs is not None:
        experiment_params['warmup_epochs'] = args.warmup_epochs
        overrides_applied.append(f"warmup_epochs={args.warmup_epochs}")
    if args.main_epochs is not None:
        experiment_params['main_epochs'] = args.main_epochs
        overrides_applied.append(f"main_epochs={args.main_epochs}")
    if args.validation_freq is not None:
        experiment_params['validation_freq'] = args.validation_freq
        overrides_applied.append(f"validation_freq={args.validation_freq}")
    if args.results_dir is not None:
        experiment_params['results_dir'] = args.results_dir
        overrides_applied.append(f"results_dir={args.results_dir}")
    
    # 混合精度设置
    if args.mixed_precision:
        experiment_params['enable_mixed_precision'] = True
        overrides_applied.append("enable_mixed_precision=True")
    elif args.no_mixed_precision:
        experiment_params['enable_mixed_precision'] = False
        overrides_applied.append("enable_mixed_precision=False")
    
    # 性能监控设置
    if args.profiling:
        experiment_params['enable_profiling'] = True
        overrides_applied.append("enable_profiling=True")
    elif args.no_profiling:
        experiment_params['enable_profiling'] = False
        overrides_applied.append("enable_profiling=False")
    
    if args.adaptive_batch:
        experiment_params['enable_adaptive_batch_size'] = True
        overrides_applied.append("enable_adaptive_batch_size=True")
    
    # 应用覆盖
    if physics_params or network_params or control_params or experiment_params:
        config = create_custom_config(
            physics_params=physics_params or None,
            network_params=network_params or None,
            control_params=control_params or None,
            experiment_params=experiment_params or None,
            base_config=args.config
        )
        
        if overrides_applied:
            logger.info(f"⚙️  应用命令行覆盖: {', '.join(overrides_applied)}")
    
    return config


# ============================================================================
# Result Processing / 结果处理
# ============================================================================

def save_experiment_results(config: PendulumExperimentConfig,
                          results: Dict[str, Any],
                          args: argparse.Namespace) -> None:
    """
    Save experiment results with comprehensive metadata
    保存实验结果和完整的元数据
    
    Args:
        config: 实验配置
        results: 实验结果
        args: 命令行参数
    """
    results_dir = Path(config.results_dir)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    try:
        # 保存配置
        config_path = results_dir / f"config_{timestamp}.json"
        save_config(config, config_path)
        logger.info(f"📋 配置已保存: {config_path}")
        
        # 保存命令行参数
        args_path = results_dir / f"cli_args_{timestamp}.json"
        args_dict = {k: v for k, v in vars(args).items() if v is not None}
        with open(args_path, 'w', encoding='utf-8') as f:
            json.dump(args_dict, f, indent=2, ensure_ascii=False)
        logger.info(f"🖥️  命令行参数已保存: {args_path}")
        
        # 保存系统信息
        system_info = {
            "jax_version": jax.__version__,
            "jax_devices": [str(d) for d in jax.devices()],
            "python_version": sys.version,
            "timestamp": timestamp,
            "platform": os.name,
            "cwd": str(Path.cwd())
        }
        
        system_path = results_dir / f"system_info_{timestamp}.json"
        with open(system_path, 'w', encoding='utf-8') as f:
            json.dump(system_info, f, indent=2, ensure_ascii=False)
        logger.info(f"💻 系统信息已保存: {system_path}")
        
        logger.info(f"✅ 所有结果已保存到: {results_dir}")
        
    except Exception as e:
        logger.error(f"❌ 保存结果时出错: {e}")
        raise


def print_experiment_summary(config: PendulumExperimentConfig,
                           results: Dict[str, Any],
                           runtime: float) -> None:
    """
    Print experiment summary and results
    打印实验摘要和结果
    
    Args:
        config: 实验配置
        results: 实验结果
        runtime: 运行时间（秒）
    """
    print("\n" + "=" * 80)
    print("🎉 实验完成摘要 / Experiment Completion Summary")
    print("=" * 80)
    
    print(f"\n⏱️  运行时间: {runtime:.2f}秒 ({runtime/60:.1f}分钟)")
    print(f"📁 结果目录: {config.results_dir}")
    print(f"🎲 随机种子: {config.random_seed}")
    
    # 训练结果
    if 'final_validation' in results:
        validation = results['final_validation']
        print(f"\n📊 最终验证指标:")
        if 'mean_winding_number' in validation:
            print(f"  缠绕数: {validation['mean_winding_number']:.4f} ± {validation.get('std_winding_number', 0):.4f}")
        if 'circular_variance' in validation:
            print(f"  圆周方差: {validation['circular_variance']:.4f}")
        if 'mean_energy' in validation:
            print(f"  平均能量: {validation['mean_energy']:.4f}")
        if 'max_constraint_violation' in validation:
            print(f"  约束违反: {validation['max_constraint_violation']:.2e}")
    
    # 配置摘要
    print(f"\n🔧 配置摘要:")
    print(f"  批处理大小: {config.control_config.batch_size}")
    print(f"  学习率: {config.control_config.learning_rate}")
    print(f"  训练轮数: {config.warmup_epochs + config.main_epochs}")
    print(f"  混合精度: {'启用' if config.enable_mixed_precision else '禁用'}")
    print(f"  性能监控: {'启用' if config.enable_performance_monitoring else '禁用'}")
    
    print("=" * 80)


# ============================================================================
# Main Experiment Function / 主实验函数
# ============================================================================

def run_experiment(config: PendulumExperimentConfig,
                  jax_key: jax.random.PRNGKey,
                  verbose: bool = True) -> Dict[str, Any]:
    """
    Run the complete MMSBVI experiment
    运行完整的MMSBVI实验
    
    Args:
        config: 实验配置
        jax_key: JAX随机密钥
        verbose: 是否详细输出
        
    Returns:
        实验结果字典
    """
    if verbose:
        print_config_summary(config)
    
    logger.info("🚀 开始实验编排器初始化")
    
    # 创建实验编排器
    orchestrator = PendulumExperimentOrchestrator(config)
    
    logger.info("🎯 开始实验运行")
    start_time = datetime.now()
    
    # 运行实验
    results = orchestrator.run()
    
    end_time = datetime.now()
    runtime = (end_time - start_time).total_seconds()
    
    logger.info(f"✅ 实验运行完成，用时 {runtime:.2f}秒")
    
    # 添加运行时信息到结果中
    results['runtime_seconds'] = runtime
    results['start_time'] = start_time.isoformat()
    results['end_time'] = end_time.isoformat()
    
    return results


# ============================================================================
# Main Entry Point / 主入口点
# ============================================================================

def main() -> None:
    """
    Main entry point for MMSBVI pendulum experiment
    MMSBVI单摆实验的主入口点
    """
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志级别
        if args.quiet:
            logger.setLevel(30)  # WARNING
        elif args.verbose:
            logger.setLevel(10)  # DEBUG
        else:
            logger.setLevel(20)  # INFO
        
        # 欢迎信息
        logger.info("🧪 大角度单摆MMSBVI实验启动 / Large-Angle Pendulum MMSBVI Experiment Starting")
        logger.info(f"📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 设置JAX环境
        setup_jax_environment(use_gpu=not args.use_cpu, memory_fraction=args.memory_fraction)
        
        # 设置随机种子
        jax_key = set_random_seeds(args.random_seed)
        
        # 创建配置
        config = create_config_from_args(args)
        
        # 验证配置
        logger.info("🔍 验证配置...")
        validate_config(config)
        logger.info("✅ 配置验证通过")
        
        # 保存配置（如果指定）
        if args.save_config:
            save_config(config, args.save_config)
            logger.info(f"💾 配置已保存: {args.save_config}")
        
        # 运行实验
        results = run_experiment(config, jax_key, verbose=not args.quiet)
        
        # 保存结果
        logger.info("💾 保存实验结果...")
        save_experiment_results(config, results, args)
        
        # 打印摘要
        if not args.quiet:
            print_experiment_summary(config, results, results['runtime_seconds'])
        
        logger.info("🎉 实验成功完成！/ Experiment completed successfully!")
        
    except KeyboardInterrupt:
        logger.warning("⚠️  实验被用户中断")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ 实验失败: {str(e)}")
        if args.verbose if 'args' in locals() else True:
            logger.error(f"错误详情:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()