# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MMSBVI (Multi-Marginal Schrödinger Bridge Variational Inference) is a JAX-based implementation for variational inference in path space through optimal transport theory. The project establishes equivalence between Bayesian smoothing and Multi-Marginal Schrödinger Bridge problems.

## Development Commands

### Environment Setup
```bash
# Automatic installation (recommended)
python setup_environment.py

# Manual installation - CPU
pip install -r requirements-cpu.txt

# Manual installation - GPU  
pip install -r requirements-gpu.txt
```

### Testing
```bash
# Run all tests
pytest tests/

# Run specific test categories
pytest tests/test_control_grad.py      # Neural control solver tests
pytest tests/test_integrators.py      # SDE integration tests
pytest tests/test_rts_*               # RTS equivalence tests
pytest tests/test_mathematical_validation.py  # Theoretical validation
```

### Validation Workflows
```bash
# Complete validation suite (reproduces paper results)
chmod +x automation/run_complete_validation_suite.sh
./automation/run_complete_validation_suite.sh

# Individual validation workflows
./automation/run_rts_equivalence_workflow.sh      # RTS smoother equivalence
./automation/run_geometric_limits_workflow.sh     # Geometric convergence analysis
./automation/run_parameter_sensitivity_workflow.sh # Parameter sensitivity
```

### Code Quality
```bash
# Format code
black src/ tests/ --line-length 88
isort src/ tests/ --profile black

# Type checking
mypy src/mmsbvi/

# Linting
flake8 src/ tests/
```

## Architecture Overview

### Core Directories
- `src/mmsbvi/` - Main library code
  - `algorithms/` - Core solvers (IPFP grid solver, neural control solver)
  - `core/` - Type definitions, configurations, component registry
  - `solvers/` - Numerical solvers (PDE, Gaussian kernel)
  - `nets/` - Neural network architectures (Flax-based)
  - `integrators/` - SDE numerical integration schemes
- `src/baselines/` - Baseline methods (EKF, UKF, GPSSM)
- `theoretical_verification/` - 1D theoretical validation experiments
- `tests/` - Comprehensive test suite
- `automation/` - Automated validation workflows

### Key Components

1. **Dual Solver Architecture**:
   - `ipfp_1d.py`: High-precision grid-based IPFP solver for theoretical validation
   - `control_grad.py`: Neural network-based control solver for scalability

2. **Type System**: Uses `chex.dataclass` and `jaxtyping` for type safety
   - `MMSBProblem`: Problem definition
   - `IPFPConfig`/`ControlGradConfig`: Algorithm configurations  
   - `MMSBSolution`: Solution representation

3. **Component Registry**: Factory pattern for dynamic loading via configuration

## JAX Performance Guidelines

**Critical Reference**: The project includes `JAX.md` (4300+ lines) containing comprehensive JAX performance optimization guidelines. Key principles:
- Use `jax.jit` for compilation
- Leverage `vmap` for vectorization
- Enable 64-bit precision for theoretical work: `jax.config.update('jax_enable_x64', True)`
- Use profiling tools for performance analysis
- Follow memory-efficient patterns for large-scale computations

## Development Patterns

### Configuration Management
- Uses Hydra for configuration management
- Config files located in `src/mmsbvi/configs/`
- Component registration via string names in `registry.py`

### Numerical Considerations
- All theoretical validation requires 64-bit precision
- Gradient checkpointing used for memory efficiency
- Mixed-precision training available for neural solvers

### Testing Strategy
- Mathematical validation tests for theoretical correctness
- Performance benchmarks for regression testing
- Stress tests for numerical stability
- Integration tests for end-to-end workflows

## Important Notes

- **Hardware Requirements**: Supports CPU, GPU (CUDA), and TPU execution
- **Dependencies**: Core dependencies include JAX ecosystem, OTT-JAX for optimal transport, BlackJAX for MCMC baselines
- **Results Location**: Validation results saved to `results/` directory, organized by experiment type
- **Paper Reproduction**: All figures and numerical results from the associated paper can be reproduced via automation scripts

## Troubleshooting

- For CUDA issues, check JAX installation with correct CUDA version
- For memory issues on Mac, use `jax.config.update('jax_memory_fraction', 0.5)`
- For precision issues, ensure 64-bit mode is enabled for theoretical work
- Consult `JAX.md` for detailed performance optimization guidance （This file is very large, please do not read it all at once. Use search tools to read as needed. Read the first 30 lines to understand the document struture）
- Do not use any Python operations (we only use Python as a glue language). Perform a more comprehensive optimization for GPU (CUDA). First, read the first 50 lines of JAX.md to understand the guidelines for JAX best practices (which you will need to refer to when coding).
- Hold yourself to strict standards as an Anthropic Principal Engineer and the chairperson (and chief professor) of the Department of Mathematics at Princeton University.
- You MUST think deeply before replying to the question.