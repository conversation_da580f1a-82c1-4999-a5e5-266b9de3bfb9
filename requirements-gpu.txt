# --- Core JAX with CUDA 12 Support (Fixed Version) ---
# 统一版本：与CPU版本完全一致，解决兼容性问题
jax[cuda12]==0.6.2
jaxlib==0.6.2

# --- Core Scientific Computing Libraries (Version Aligned with CPU) ---
ott-jax==0.4.5
blackjax==1.2.5
optax==0.1.9
flax==0.8.5
chex==0.1.85
jaxtyping==0.2.25
jaxopt==0.8.5

# --- Numerical Libraries (Fixed Versions) ---
numpy==1.26.3
scipy==1.12.0
ml_dtypes==0.5.1

# --- GPU Monitoring & Profiling Tools (补充之前缺失的) ---
nvidia-ml-py==12.575.51
py-spy==0.4.0
memory-profiler==0.61.0
psutil==5.9.6

# --- Machine Learning Infrastructure ---
orbax-checkpoint==0.4.4
einops==0.7.0

# --- Configuration & Orchestration ---
hydra-core==1.3.2
omegaconf==2.3.0

# --- Data Processing ---
pandas==2.3.0
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
pillow==11.3.0

# --- Development & Testing ---
pytest==8.4.1
pytest-xdist==3.8.0
mypy==1.16.1
mypy_extensions==1.1.0

# --- Utilities ---
rich==13.7.0
click==8.2.1
pathspec==0.12.1
packaging==25.0
typing_extensions==4.14.1

# --- System Libraries ---
absl-py==2.3.1
protobuf==4.25.8
certifi==2025.6.15
charset-normalizer==3.4.2
urllib3==2.5.0
requests==2.32.4
idna==3.10

# --- Additional Scientific Libraries ---
contourpy==1.3.2
cycler==0.12.1
fonttools==4.58.5
kiwisolver==1.4.8
pyparsing==3.2.3
python-dateutil==2.9.0.post0
PyYAML==6.0.2
pytz==2025.2
six==1.17.0
tzdata==2025.2

# --- Experiment Tracking ---
wandb==0.16.1
sentry-sdk==2.32.0

# --- File Processing ---
openpyxl==3.1.5
et_xmlfile==2.0.0

# --- Development Tools ---
gitdb==4.0.12
GitPython==3.1.44
smmap==5.0.2

# --- Async & Parallel Processing ---
execnet==2.1.1
fastprogress==1.0.3

# --- Misc Dependencies ---
tenacity==9.1.2
toolz==1.0.0
typeguard==2.13.3
setproctitle==1.3.6
markdown-it-py==3.0.0
mdurl==0.1.2
Pygments==2.19.2
pluggy==1.6.0
iniconfig==2.1.0
docker-pycreds==0.4.0
appdirs==1.4.4
antlr4-python3-runtime==4.9.3
opt_einsum==3.4.0

# --- Optional GPU Packages (uncomment if needed) ---
# numba  # GPU加速数值计算
# gpustat  # GPU监控工具